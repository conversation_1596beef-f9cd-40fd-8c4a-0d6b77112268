{"name": "cg-audit-client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "vite --host 0.0.0.0", "dev": "vite --host 0.0.0.0", "build:development": "vite build --mode development", "build:staging": "vite build --mode staging", "build:production": "vite build --mode production", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ckeditor/ckeditor5-build-classic": "^44.3.0", "@ckeditor/ckeditor5-react": "^9.5.0", "@headlessui/react": "^1.7.18", "@reduxjs/toolkit": "^2.3.0", "@tanstack/match-sorter-utils": "^8.19.4", "@tanstack/react-table": "^8.20.5", "axios": "^1.7.7", "classnames": "^2.5.1", "flatpickr": "^4.6.13", "i18next": "^23.16.2", "i18next-browser-languagedetector": "^8.0.0", "lucide-react": "^0.453.0", "react": "^18.3.1", "react-arborist": "^3.4.3", "react-dom": "^18.3.1", "react-flatpickr": "^3.10.13", "react-hook-form": "^7.53.2", "react-i18next": "^15.1.0", "react-quill": "^2.0.0", "react-redux": "^9.1.2", "react-router-dom": "^6.27.0", "react-select": "^5.8.2", "react-toastify": "^11.0.0", "remixicon": "^4.4.0", "sass": "^1.80.3", "simplebar-react": "^3.2.6", "sweetalert2": "^11.15.1", "swiper": "^11.1.14"}, "devDependencies": {"@eslint/js": "^9.11.1", "@types/react": "^18.3.10", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.11.1", "eslint-plugin-react": "^7.37.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.12", "globals": "^15.9.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.13", "vite": "^5.4.8"}}