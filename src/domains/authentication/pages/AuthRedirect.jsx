import { useEffect, useMemo, useState } from "react";
import { useDispatch } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import { authenticate } from "../redux/slices/AuthAction";
import Loader from "../../../shared/components/Loading/Loader";
import { fetchUserDetails } from "../../user/redux/slices/userAction";
import { useFilteredMenuData } from "../../../shared/layout/navigation/TabsConfig";

const AuthRedirect = () => {
    const dispatch = useDispatch();
    const location = useLocation();
    const navigate = useNavigate();
    const [loading, setLoading] = useState(true);

    // Fetch user details
    useEffect(() => {
        const fetchData = async () => {
            console.log('first effect')
            const allotId = localStorage.getItem("allotId");
            if (allotId) {
                await dispatch(fetchUserDetails({ allotId })).unwrap();
            }
            setLoading(false);
        };
        fetchData();
    }, [dispatch]);

    // Use filtered menu data hook
    const filteredMenuData = useFilteredMenuData();
    console.log('filteredMenuData', filteredMenuData)

    // Memoize first allowed link calculation
    const firstAllowedLink = useMemo(() => {
        if (!filteredMenuData || filteredMenuData.length === 0) return null;

        for (let menu of filteredMenuData) {
            if (menu.subItems && menu.subItems.length > 0) {
                return menu.subItems[0].link;
            } else if (menu.link) {
                return menu.link;
            }
        }
        return null;
    }, [filteredMenuData]);

    console.log('firstAllowedLink', firstAllowedLink)

    // Redirect based on token and privileges
    useEffect(() => {
        console.log('2nd useEffect')
        if (loading) return;
        console.log('2nd useEffect IN')
        const handleRedirect = async () => {
            console.log('handleRedirect')
            const token = localStorage.getItem("accessToken");
            // const { active } = await introspectToken(token);
            if (token) {
                console.log('token')
                console.log('token-firstAllowedLink', firstAllowedLink)
                if (firstAllowedLink) {
                    console.log('firstAllowedLink 2ND', firstAllowedLink)
                    navigate(firstAllowedLink);
                    return;
                }
            } else {
                localStorage.removeItem("accessToken");
                localStorage.removeItem("refreshToken");
                localStorage.removeItem("allotId");

              
            }

            const params = new URLSearchParams(location.search);
            const code = params.get("code");
            console.log('code', code)
            if (code) {
                console.log('code IN')
                await dispatch(authenticate(code)).unwrap();
                if (firstAllowedLink) {
                    navigate(firstAllowedLink);
                    return;
                }
            } else {
                console.log('ERROR')
                navigate("/core/welcome");
            }


        };

        handleRedirect();
    }, [loading, firstAllowedLink, dispatch, navigate, location.search]);

    if (loading) {
        return <Loader text="Signing In..." />;
    }

    return null;
};

export default AuthRedirect;
