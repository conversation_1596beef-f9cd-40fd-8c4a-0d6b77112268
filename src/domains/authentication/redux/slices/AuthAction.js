import { createAsyncThunk } from "@reduxjs/toolkit";
import { authenticateUser } from '../../../authentication/services/authService';


// Async thunk to handle authentication
export const authenticate = createAsyncThunk(
    'auth/authenticate',
    async (code, { rejectWithValue }) => {
        try {
            const data = await authenticateUser(code);
            // Store tokens in localStorage
            localStorage.setItem('accessToken', data.accessToken);
            localStorage.setItem('refreshToken', data.refreshToken);
            return data;
        } catch (error) {
            return rejectWithValue(error);
        }
    }
);