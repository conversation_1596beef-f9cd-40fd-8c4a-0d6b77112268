import { createSlice } from "@reduxjs/toolkit";
import {authenticate} from "./AuthAction"

const initialState = {
    accessToken: null,
    refreshToken: null,
    userDetails: null,
    allotId: null,
    loading: false,
    error: null,
};


const authSlice = createSlice({
    name: 'auth',
    initialState,
    reducers: {
        logout: (state) => {
            state.accessToken = null;
            state.refreshToken = null;
            state.userDetails = null;
            state.allotId = null;
            localStorage.removeItem('accessToken');
            localStorage.removeItem('refreshToken');
            localStorage.removeItem('allotId');
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(authenticate.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(authenticate.fulfilled, (state, action) => {
                state.loading = false;
                state.accessToken = action.payload.accessToken;
                state.refreshToken = action.payload.refreshToken;
                state.userDetails = action.payload.userDetails;
                state.allotId = action.payload.allotId;
            })
            .addCase(authenticate.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload;
            })
    },

})

export const { logout } = authSlice.actions;

export default authSlice.reducer;