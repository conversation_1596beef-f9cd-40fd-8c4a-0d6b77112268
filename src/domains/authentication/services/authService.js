import axios from "axios";
import * as url from '../../../shared/api/url';

const CLIENT_ID = import.meta.env.VITE_CLIENT_ID || ""
const CLIENT_SECRET = import.meta.env.VITE_CLIENT_SECRET;
const REDIRECT_URI = import.meta.env.VITE_REDIRECT_URI;
const GRANT_TYPE = import.meta.env.VITE_GRANT_TYPE;

export const authenticateUser = async (code) => {
    try {
        const { access_token: accessToken, refresh_token: refreshToken } = await getToken(code);
        const { allot_id: allotId } = await getUserProfile(accessToken);
        
        return {
            accessToken,
            refreshToken,
            allotId,
            userDetails: null, // Update this to userDetails if Step 3 is enabled
        };
    } catch (error) {
        console.log(error);
    }
}

const getToken = async (code) => {
    const params = new URLSearchParams({
        client_id: CLIENT_ID,
        client_secret: CLIENT_SECRET,
        redirect_uri: REDIRECT_URI,
        grant_type: GRANT_TYPE,
        code,
    });
    const response = await axios.post(url.AUTH_REST_API_BASE_URL, params, {
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
    });

    return response.data;
};

const getUserProfile = async (accessToken) => {
    const response = await axios.get(`${url.PROFILE_API_BASE_URL}?token=${accessToken}`);
    return response.data.attributes;
};

export const introspectToken = async () => {
  const token = localStorage.getItem('accessToken');
  const params = new URLSearchParams({
    token,
  });

  const username = "oidc_client";
  const password ="P7AmlPUl3N";

  const authHeader = 'Basic ' + btoa(`${username}:${password}`);

  const response = await axios.post(`${import.meta.env.VITE_CAS_BASE_URL}${url.AUTH_TOKEN_INTROSPECT}`, params, {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Authorization': authHeader
    },
  });
  const { active} = response.data;
  return { active};
}