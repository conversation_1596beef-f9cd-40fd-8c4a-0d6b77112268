import { apiClient } from '../../../shared/api/axiosInstance';
import * as url from '../../../shared/api/url';


export const getInitiatedAuditList = ({ page, size, finYear }) => apiClient.get(url.GET_PENDING_LIST, { page, size, finYear });
export const getSubmittedAuditList = ({ page, size, finYear }) => apiClient.get(url.GET_SUBMITTED_LIST, {page, size, finYear});
export const getRepliedAuditList = ({ page, size }) => apiClient.get(url.GET_REPLIED_LIST, page, size);
export const getReceivedAuditList = ({ page, size,seatId ,finYear}) => apiClient.get(url.GET_RECEIVED_LIST, { page, size,seatId,finYear });

export const saveOrUpdateInspectionReport = (formData) => apiClient.create(url.SAVE_INSPECTION_REPORT, formData);
export const getInspectionReportData = (id) => apiClient.get(url.GET_INSPECTION_REPORT, { id });

export const saveOrUpdateObservation = (formData) => apiClient.create(url.SAVE_OBSERVATION, formData);
export const getObservations = (id) => apiClient.get(url.GET_OBSERVATION_LIST, { id });
export const saveAuditObservation = (formData) => apiClient.create(url.SAVE_AUDIT_OBSERVATION, formData);

export const getNotes = (id) => apiClient.get(url.GET_NOTES, { id });
export const saveOrUpdateNotes = (formData) => apiClient.create(url.SAVE_NOTES, formData)


export const downloadInspectionReport = () => apiClient.get(url.DOWNLOAD_INSPECTION_REPORT, {}, { responseType: 'arraybuffer' });

export const getAuditHistory = (id) => apiClient.get(url.AUDIT_HISTORY, { id });

export const submitAuditProposalToUser = (formData) => apiClient.create(url.SUBMIT_AUDIT, formData);

export const saveQueryAndResponse = (formData) => apiClient.create(url.SAVE_QUERY, formData);
export const getQueriedData = (id) => apiClient.get(url.QUERIED_DATA,{id})



