import React, { useEffect, useState } from "react"
import { getNotes, saveOrUpdateNotes } from "../services/auditService"
import Modal from "../../../shared/components/modal";
import TextField from "../../../shared/components/form/TextField";
import { useForm } from "react-hook-form";
import { Search } from "lucide-react";
import { useSelector } from "react-redux";
import NoteSkeletonLoader from "../../../shared/components/Loading/NoteSkeletonLoader";

const Notes = ({ inspectionData, key }) => {

  const [loading, setLoading] = useState(false);
  const [notes, setNotes] = useState([]);
  const [show, setShow] = useState(false);
  const [data, setData] = useState(null);
  // const { allotId } = useSelector((state) => state.User?.userDetails);
  const  allotId  = localStorage.getItem('allotId')

  console.log("inspectionDatainspectionData",inspectionData)

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm();

  const toggle = () => {
    if (show) {
      setShow(false);
    } else {
      setShow(true);
    }
  }
  useEffect(() => {
    const fetchNotes = async () => {
      setLoading(true)
      try {
        const response = await getNotes(inspectionData.id);
        console.log(response,"responseresponse")
        setNotes(response || []);
      } catch (error) {
        console.log(error)
        setNotes([]);
      } finally {
        setLoading(false)
      }
    }
    fetchNotes();

  }, [inspectionData.id,data,key]);


  const onSubmit = async (data) => {
    setLoading(true);
    try {
      const modifiedData = {
        ...data,
        inspectionReportId: inspectionData.id,
        enteredBy: allotId

      }
      console.log(modifiedData,"modifiedData")
      // const response = await saveOrUpdateNotes(modifiedData);
      // setData(response.data)
      // setValue(response.data);
    } catch (error) {
      console.log(error)
    } finally {
      setLoading(false);
      toggle();
    }
  }
 
  if (loading) {
    return <NoteSkeletonLoader />
  }
  return (
    <React.Fragment>
      {/* <div className="flex justify-between border-b pb-4">
        <h2 className="text-2xl font-bold">Notes</h2>
        <button type="button" className="bg-white border-dashed text-sky-500 btn border-sky-500 hover:text-sky-500 hover:bg-sky-50 hover:border-sky-600 focus:text-sky-600 focus:bg-sky-50 focus:border-sky-600 active:text-sky-600 active:bg-sky-50 active:border-sky-600 dark:bg-zink-700 dark:ring-sky-400/20 dark:hover:bg-sky-800/20 dark:focus:bg-sky-800/20 dark:active:bg-sky-800/20" onClick={toggle}><i className="align-baseline ltr:pr-1 rtl:pl-1 ri-add-line"></i> Add Note</button>
      </div> */}

      <div className="overflow-x-auto p-4 space-y-4">
      {notes && notes.length > 0 ?
        notes.map((note) => (
          <div
            key={note.id}
            className="grid grid-cols-1 gap-4 p-4 border border-blue-200 mb-2 rounded-lg"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <div className="p-4">
                  <p className="text-slate-500 dark:text-zink-200"></p>
                  <h6 className="text-muted mb-4">{note.designation}</h6>
                  <ul className="flex flex-col gap-1">
                    <li>{note.note}</li>
                  </ul>
                  <div className="flex items-center justify-between gap-3 pt-4 mt-auto">
                    <div className="shrink-0 text-blue-500 dark:text-zink-200">{note.username}</div>
                    <p className="text-blue-500 dark:text-zink-200 shrink-0">{note.date}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )) : (<div className="noresult">
          <div className="py-6 text-center">
            <Search className="size-6 mx-auto text-sky-500 fill-sky-100 dark:sky-500/20" />
            <h5 className="mt-2 mb-1"> No Notes Found</h5>
            <p className="mb-0 text-slate-500 dark:text-zink-200">Click on <span className="text-red-400 font-semibold">ADD NOTE</span>  button to create new note</p>
          </div>
        </div>)}
      </div>
      <Modal show={show} onHide={toggle} modal-center="true"
        className="fixed flex flex-col transition-all duration-300 ease-in-out left-2/4 z-drawer -translate-x-2/4 -translate-y-2/4"
        dialogClassName="w-screen md:w-[30rem] bg-white shadow rounded-md dark:bg-zink-600">
        <Modal.Header className="flex items-center justify-between p-4 border-b dark:border-zink-500"
          closeButtonClass="transition-all duration-200 ease-linear text-slate-400 hover:text-red-500">
          <Modal.Title className="text-16"> Add Notes</Modal.Title>
        </Modal.Header>
        <Modal.Body className="max-h-[calc(theme('height.screen')_-_180px)] p-4 overflow-y-auto">
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="grid grid-cols-1 gap-4 xl:grid-cols-12">
              <div className="xl:col-span-12">
                <TextField
                  label="Notes"
                  name="note"
                  register={register}
                  validation={{ required: "Please Enter Notes Before Save" }}
                  placeholder="Enter Notes Here"
                  error={errors.note}
                  multiline
                />

              </div>
            </div>
            <div className="flex justify-end gap-2 mt-4">
              <button type="reset" data-modal-close="addHolidayModal" className="text-red-500 bg-white btn hover:text-red-500 hover:bg-red-100 focus:text-red-500 focus:bg-red-100 active:text-red-500 active:bg-red-100 dark:bg-zink-600 dark:hover:bg-red-500/10 dark:focus:bg-red-500/10 dark:active:bg-red-500/10" onClick={toggle}>Cancel</button>
              <button type="submit" className="text-white btn bg-custom-500 border-custom-500 hover:text-white hover:bg-custom-600 hover:border-custom-600 focus:text-white focus:bg-custom-600 focus:border-custom-600 focus:ring focus:ring-custom-100 active:text-white active:bg-custom-600 active:border-custom-600 active:ring active:ring-custom-100 dark:ring-custom-400/20">
                Save
              </button>
            </div>
          </form>
        </Modal.Body>
      </Modal>
    </React.Fragment>
  )
}

export default Notes