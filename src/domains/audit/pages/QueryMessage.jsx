
import { Link } from 'react-router-dom';
import user from '../../../shared/assets/images/user/anup.jpg'
const QueryMessage = ({ sender, senderName, senderLink, time, message }) => (
    <div className="flex gap-3 mt-3 first:mt-0">
        <div className="relative flex items-center justify-center font-semibold rounded-full text-slate-500 size-9 bg-slate-100 shrink-0 dark:text-zink-200 dark:bg-zink-600">
            <img src={user} alt={sender} className="rounded-full h-9" />
        </div>
        <div className="grow">
            <div className="flex items-center">
                <div className="grow">
                    <h6>{sender}</h6>
                    <p className="text-slate-500 dark:text-zink-200">
                        <Link to={senderLink}>{senderName}</Link>
                    </p>
                </div>
                <div className="shrink-0">{time}</div>
            </div>
            <div className="p-4 mt-3 rounded-md bg-slate-100 dark:bg-zink-600">
                {message.map((paragraph, idx) => (
                    <p key={idx} className="mb-2 last:mb-0">{paragraph}</p>
                ))}
            </div>
        </div>
    </div>
);

export default QueryMessage