import { View } from "lucide-react";
import React from "react";
import { ViewItem } from "../../../shared/components/form/ViewItem";
import AuditEntry from "./AuditEntry";

const AuditDetails = ({inspectionData}) => {


  
  return (
    <React.Fragment>

      {inspectionData.agSubmittedBy != null ? (
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-4">

          <div className="lg:col-span-6">
            <ViewItem label="Audit Name" value={inspectionData?.auditName || "N/A"} />
          </div>
          <div className="lg:col-span-6">
            <ViewItem label="Audit Type" value={inspectionData?.typeSetup.id || "N/A"} />
          </div>
          <div className="lg:col-span-6">
            <ViewItem label="Audit Sub Type" value={inspectionData?.auditSubTypeSetup.id || "N/A"} />
          </div>
          <div className="lg:col-span-6">
            <ViewItem label="Financial Year" value={inspectionData?.finYear || "N/A"} />
          </div>
          <div className="lg:col-span-6">
            <ViewItem label="Audit from Date" value={inspectionData?.auditFromDate || "N/A"} />
          </div>
          <div className="lg:col-span-6">
            <ViewItem label="Audit to Date" value={inspectionData?.auditToDate || "N/A"} />
          </div>
          <div className="lg:col-span-6">
            <ViewItem label="Inspection Report" value={inspectionData?.inspectionReport || "N/A"} />
          </div>
          <div className="lg:col-span-6">
            <ViewItem label="Department" value={inspectionData?.departmentName || "N/A"} />
          </div>
          <div className="lg:col-span-6">
            <ViewItem label="Directorates" value={inspectionData?.directorateName || "N/A"} />
          </div>

        </div>) : (<AuditEntry id={inspectionData.id} gridSpan='xl:col-span-6' />)}

    </React.Fragment>
  );
};

export default AuditDetails;
