import React, { useEffect, useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { Plus, Trash2 } from "lucide-react";
import TextField from "../../../shared/components/form/TextField";
import { saveOrUpdateObservation, getObservations } from "../services/auditService"
import Loader from "../../../shared/components/Loading/Loader";

const Observation = ({inspectionData}) => {
  const [loading, setLoading] = useState(false);


  const { register, control, handleSubmit, formState: { errors }, setValue } = useForm({
    defaultValues: {
      audits: [{ id: null, observationNo: "", attachmentName: "", description: "", observationTitle: "", attachment: "" }], // Start with one row
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: "audits",
  });

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        if (inspectionData.id) {
          const response = await getObservations(inspectionData.id);
          if (response.length > 0) {
            const formattedData = response.map(item => ({
              id: item.id || null,
              observationNo: item.observationNo || "",
              attachmentName: item.attachmentName || "",
              description: item.description || "",
              observationTitle: item.observationTitle || "",
              attachment: item.attachment || "",
            }));
            setValue("audits", formattedData);
          }
        }
      } catch (error) {
        console.error("Error fetching observations:", error);
      }
      finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [inspectionData.id, setValue]);


  const onSubmit = async (data) => {
    setLoading(true);
    try {
      const formattedData = data.audits.map((audit) => ({
        id: parseInt(audit.id) || null,
        inspectionReportId : inspectionData.id,
        observationNo: audit.observationNo,
        observationTitle: audit.observationTitle,
        description: audit.description,
        attachmentName: audit.attachmentName,
        attachment: audit.attachment,
      }));
      const response = await saveOrUpdateObservation(formattedData);
      setValue(response.data);
    } catch (error) {
      console.log("Error saving data", error);
    } finally {
      setLoading(false);
    }
  }

  if (loading) {
    return <Loader />
  }

  return (
    <React.Fragment>
      <div className="flex justify-between border-b pb-4">
        <h2 className="text-2xl font-bold">Observation Details</h2>
        <button
          type="button"
          onClick={() => append({ auditName: "", type: "", subType: "" })}
          className=" mr-4 flex items-center justify-center size-[37.5px] p-0 text-green-500 bg-green-100 btn hover:text-white hover:bg-green-600 focus:text-white focus:bg-green-600 focus:ring focus:ring-green-100 active:text-white active:bg-red-600 active:ring active:ring-red-100 dark:bg-green-500/20 dark:text-green-500 dark:hover:bg-green-500 dark:hover:text-white dark:focus:bg-green-500 dark:focus:text-white dark:active:bg-green-500 dark:active:text-white dark:ring-green-400/20"
        >
          <Plus className="size-4" />
        </button>
      </div>
      <form onSubmit={handleSubmit(onSubmit)} className="p-4 space-y-4">
        <div className="overflow-x-auto">
          <table className="min-w-full table-auto">
            <tbody>
              {fields.map((item, index) => (
                <tr key={item.id} className="grid grid-cols-1 gap-4 p-4 border border-blue-200 mb-2 rounded-lg">
                  <td className="col-span-1">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <TextField
                        label="Observation Number"
                        name={`audits.${index}.observationNo`}
                        register={register}
                        validation={{ required: "Observation Number required" }}
                        error={errors.audits?.[index]?.observationNo}
                        placeholder="Observation Number"
                        labelClass="font-bold text-sky-800"
                      />
                      <TextField
                        label="Observation Title"
                        name={`audits.${index}.observationTitle`}
                        register={register}
                        validation={{ required: "Observation Title is required" }}
                        error={errors.audits?.[index]?.observationTitle}
                        placeholder="Observation Title"
                        labelClass="font-bold text-sky-800"
                      />
                      <TextField
                        label="Attachment Name"
                        name={`audits.${index}.attachmentName`}
                        register={register}
                        validation={{ required: "Objection Id is required" }}
                        error={errors.audits?.[index]?.attachmentName}
                        placeholder="Attachment Name"
                        labelClass="font-bold text-sky-800"
                      />
                      <TextField
                        label="Attachment"
                        name={`audits.${index}.attachment`}
                        register={register}
                        validation={{ required: "Attachment is required" }}
                        error={errors.audits?.[index]?.attachment}
                        placeholder="Attachment"
                        labelClass="font-bold text-sky-800"
                      />

                      <div className="md:col-span-2">
                        <TextField
                          label="Details Description"
                          name={`audits.${index}.description`}
                          register={register}
                          validation={{ required: "Description is required" }}
                          error={errors.audits?.[index]?.description}
                          placeholder="Details Description"
                          labelClass="font-bold text-sky-800"
                          multiline
                        />
                      </div>
                    </div>

                    <div className="flex justify-end mt-6 gap-x-4">
                      {index > 0 && (
                        <button
                          type="button"
                          onClick={() => remove(index)}
                          className="flex items-center justify-center size-[37.5px] p-0 text-red-500 bg-red-100 btn hover:text-white hover:bg-red-600 focus:text-white focus:bg-red-600 focus:ring focus:ring-red-100 active:text-white active:bg-red-600 active:ring active:ring-red-100 dark:bg-red-500/20 dark:text-red-500 dark:hover:bg-red-500 dark:hover:text-white dark:focus:bg-red-500 dark:focus:text-white dark:active:bg-red-500 dark:active:text-white dark:ring-red-400/20"
                        >
                          <Trash2 className="size-4" />
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <button
          type="submit"
          className="bg-blue-500 text-white px-6 py-2 rounded"
        >
          Save
        </button>
      </form>
    </React.Fragment>
  );
};

export default Observation;
