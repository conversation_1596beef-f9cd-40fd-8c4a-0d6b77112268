import React, { useEffect, useRef, useState } from 'react';
import Tab from '../../../shared/components/tab/Tab'
import { ChevronsLeft, StepBack } from 'lucide-react';
import SimpleBar from 'simplebar-react';
import { Link, useLocation } from 'react-router-dom';
import AuditDetails from './AuditDetails';
import ViewObservation from './ViewObservation';
import Notes from './Notes';
import History from './History';
import { getInspectionReportData } from '../services/auditService'
import ViewInspectionLoader from '../../../shared/components/Loading/ViewInspectionLoader';
import AddObservation from './AddObservation';
import AddNotes from './AddNotes';

const AuditView = () => {

    const [Chat_Box_Username, setChat_Box_Username] = useState("Audit Details");

    const [recentChatslist, setRecentChatslist] = useState(null);
    const [activeTab, setActiveTab] = useState(1);
    const [notesUpdateFlag, setNotesUpdateFlag] = useState(false);
    const [obsUpdateFlag, setObsUpdateFlag] = useState(false);
    const handleNotesSaved = () => {
        setNotesUpdateFlag(prev => !prev);
    };
    const handleObsSaved = () => {
        setObsUpdateFlag(prev => !prev);
    };

    const chatRef = useRef(null);

    useEffect(() => {
        if (chatRef.current?.el) {
            chatRef.current.getScrollElement().scrollTop = chatRef.current.getScrollElement().scrollHeight;
        }
    }, []);

    const userChatOpen = (ele) => {
        setChat_Box_Username(ele.name);
        document.querySelector(".menu-content")?.classList.add("hidden");
        document.querySelector(".chat-content")?.classList.add("show");
        console.log('testdata')
        setActiveTab(ele.roomId)
    };
    const retunToContact = () => {
        document.querySelector(".menu-content")?.classList.remove("hidden");
        document.querySelector(".chat-content")?.classList.remove("show");
    };

    const location = useLocation();
    const queryParams = new URLSearchParams(location.search);
    const inspectionReportId = queryParams.get('id');
    const linkTo = inspectionReportId ? `?id=${inspectionReportId}` : "#";
    const [inspectionData, setInspectionData] = useState(null);
    const [inspectionList, setInspectionList] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [isProcessing, setIsProcessing] = useState(false);

    useEffect(() => {
        const fetchInspectionData = async () => {
            setIsLoading(true);
            try {
                const response = await getInspectionReportData(inspectionReportId);
                setInspectionData(response);
                if (response && response.inspectionReportParaList) {
                    setInspectionList(response.inspectionReportParaList);
                    setIsProcessing(true);
                }
            } catch (error) {
                console.error("Error fetching data:", error);
            } finally{
                setIsLoading(false);
            }
        };
        fetchInspectionData();
    }, [inspectionReportId,obsUpdateFlag])
    console.log(inspectionList,"inspectionListinspectionList")

    const baseChatsStart = [
        { id: 1, roomId: 1, name: "Audit Details" },
        { id: 2, roomId: 2, name: "Notes" },
    ];

    const baseChatsEnd = [
        { id: 4 + (inspectionList?.length || 0), roomId: 3, name: "History" },
    ];


    useEffect(() => {
        const observationChats = inspectionList?.length > 0
            ? inspectionList.map((item, index) => ({
                id: 3 + index,
                roomId: 4 + index,
                name: `Observation ${index + 1}`, 
                observationData: item, 
            }))
            : [];
    
        const updatedChats = [...baseChatsStart, ...observationChats, ...baseChatsEnd];
    
        setRecentChatslist(updatedChats);
        setIsProcessing(false);
    }, [inspectionList,obsUpdateFlag]);


    const renderTabContent = () => {
        if (!inspectionData) return null;
        const activeChat = Array.isArray(recentChatslist) 
        ? recentChatslist.find(chat => chat.roomId === activeTab) 
        : null;
        switch (activeTab) {
            case 1:
                return <AuditDetails inspectionData={inspectionData} />;
            case 2:
                return <Notes inspectionData={inspectionData} key={notesUpdateFlag}/>;
            case 3:
                return <History inspectionData={inspectionData} />;
            default:
                if (activeChat?.observationData) {
                    return  <ViewObservation data={activeChat.observationData} />;
                }
                return <p>No content available</p>;
        }
    };
    if (isLoading || isProcessing) return <ViewInspectionLoader />
    return (
        <React.Fragment>
            <div className="container group-data-[content=boxed]:max-w-boxed mx-auto relative">
                <div className="flex gap-5 mt-5">
                    <Tab.Container defaultActiveKey='mainChatList'>
                        <div className="block w-full xl:block xl:w-80 shrink-0 menu-content">
                            <div className="h-[calc(100vh_-_theme('spacing.10')_*_6)] xl:min-h-[calc(100vh_-_theme('height.header')_*_2.4)] card xl:h-[calc(100%_-_theme('spacing.5'))]">
                                <div className="flex flex-col h-full">
                                    <Tab.Content className="tab-content">
                                        <Tab.Pane eventKey='mainChatList' className="tab-pane" id="mainChatList">
                                            <div className="card-body">
                                                <div className="flex items-center gap-3">
                                                    <h6 className="text-15 grow">Inspection Report</h6>
                                                    {/* <button data-modal-target="addContactModal" className="inline-flex items-center justify-center size-8 transition-all duration-200 ease-linear rounded-md shrink-0 bg-cg-primary text-slate-500 dark:bg-zink-600 dark:text-zink-200 hover:text-custom-500 dark:hover:text-custom-500" ><StepBack className="size-4 mx-auto text-white" /></button> */}

                                                    <Link
                                                        to="/initiated-product"
                                                        className="inline-flex items-center justify-center size-8 transition-all duration-200 ease-linear rounded-md shrink-0 bg-cg-primary text-slate-500 dark:bg-zink-600 dark:text-zink-200 hover:text-custom-500 dark:hover:text-custom-500"
                                                    >
                                                        <StepBack className="size-4 mx-auto text-white" />
                                                    </Link>
                                               
                                                </div>
                                            </div>
                                            <SimpleBar className="max-h-[calc(100vh_-_380px)] xl:max-h-[calc(100vh_-_300px)]">
                                                <ul className="flex flex-col gap-1" id="chatList">
                                                    <li className="px-5">
                                                        <p className="mb-1 text-slate-500 dark:text-zink-200">Click below</p>
                                                    </li>
                                                    {(recentChatslist || []).map((item, key) => (
                                                        <React.Fragment key={key}>
                                                            <li>
                                                                <Link
                                                                    to={linkTo}
                                                                    className={`flex items-center gap-3 px-5 py-2 [&.active]:bg-cg-primary dark:[&.active]:bg-zink-600 group/item ${item.status} ${Chat_Box_Username === item.name && "active"} ${Chat_Box_Username === item.name ? "text-white" : ""}`}
                                                                    onClick={() => userChatOpen(item)}
                                                                >
                                                                    <div className="relative flex items-center justify-center font-semibold rounded-full text-slate-500 dark:text-zink-200 size-9 bg-slate-100 dark:bg-zink-600">
                                                                        {item.img ? (
                                                                            <img src={item.img} alt="" className="rounded-full h-9" />
                                                                        ) : (
                                                                            item.name.split(' ').map((word) => word.charAt(0)).join('')
                                                                        )}
                                                                    </div>
                                                                    <div className="overflow-hidden grow">
                                                                        <h6 className={`mb-1 ${Chat_Box_Username === item.name ? "text-white" : "text-slate-500"}`}>
                                                                            {item.name}
                                                                        </h6>
                                                                    </div>
                                                                </Link>

                                                            </li>
                                                        </React.Fragment>
                                                    ))}

                                                    {(!recentChatslist || recentChatslist.length === 0) && (
                                                        <li className="px-5">
                                                            <p className="mb-1 text-slate-500 dark:text-zink-200">No Tabs Found</p>
                                                        </li>
                                                    )}


                                                </ul>
                                            </SimpleBar>
                                        </Tab.Pane>
                                    </Tab.Content>
                                </div>
                            </div>
                        </div>
                    </Tab.Container>

                    <div id='chartlist' className={`h-[calc(100vh_-_theme('spacing.10')_*_6)] xl:min-h-[calc(100vh_-_theme('height.header')_*_2.4)] card w-full hidden [&.show]:block [&.active]:xl:block chat-content active`}>
                        <div className="relative flex flex-col h-full">

                            <div className="card-body">
                                <div className="flex items-center gap-3 border-b pb-4">
                                    <button className="inline-flex items-center justify-center size-8 transition-all duration-200 ease-linear rounded-md shrink-0 bg-slate-100 text-slate-500 dark:bg-zink-600 dark:text-zink-200 hover:text-custom-500 dark:hover:text-custom-500" onClick={retunToContact}><ChevronsLeft className="size-4 mx-auto" /></button>
                                    <Link to="#!" data-drawer-target="drawerEnd" className="flex items-center gap-3 ltr:mr-auto rtl:ml-auto shrink-0" id="userChatProfile">
                                        <div>
                                            <h2 className='text-2xl font-bold'> {Chat_Box_Username}</h2>
                                        </div>
                                    </Link>
                                    <div className="flex items-center">
                                        {Chat_Box_Username === 'Audit Details' && <AddObservation inspectionData={inspectionData} onReloadObs={handleObsSaved}/>}
                                        {Chat_Box_Username === 'Notes' && <AddNotes  inspectionData={inspectionData} onReload={handleNotesSaved}/>}
                                    </div>
                                </div>
                            </div>

                            <div className="relative  dark:bg-zink-600/50 grow">
                                <SimpleBar ref={chatRef} className="h-[calc(100vh_-_410px)] xl:h-[calc(100vh_-_330px)]">
                                    <ul className="flex flex-col gap-5 list-none card-body">
                                        {renderTabContent()}
                                    </ul>
                                </SimpleBar>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </React.Fragment>
    );
}

export default AuditView;
