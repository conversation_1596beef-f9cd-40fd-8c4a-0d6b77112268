import { ArrowLeftCircle } from "lucide-react"
import React from "react"
import AuditEntry from "./AuditEntry"
import { Link } from "react-router-dom"

const AddAuditProduct = () => {
    return (
        <React.Fragment>
            <div className="card">
                <div className="card-body">
                    <div className="flex justify-between items-center mb-6">
                        <h6 className="mb-1 text-15">AUDIT PRODUCT</h6>
                        <Link
                            to="/initiated-product"
                            className="text-white btn bg-custom-500 border-custom-500 hover:text-white hover:bg-custom-600 hover:border-custom-600 focus:text-white focus:bg-custom-600 focus:border-custom-600 focus:ring focus:ring-custom-100 active:text-white active:bg-custom-600 active:border-custom-600 active:ring active:ring-custom-100 dark:ring-custom-400/20"
                        >
                            <ArrowLeftCircle className="inline-block size-4 mr-2" />
                            <span className="align-middle">Back</span>
                        </Link>
                    </div>

                    <AuditEntry/>
                </div>
            </div>
        </React.Fragment>
    )
}

export default AddAuditProduct