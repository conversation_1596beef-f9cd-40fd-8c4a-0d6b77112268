import React, { useEffect, useMemo, useState } from "react"
import BreadCrumb from "../../../shared/BreadCrumb"
import { FileEdit, MoreHorizontal, Plus, Search, Trash2 } from "lucide-react"
import { <PERSON> } from "react-router-dom"
import TableContainer from "../../../shared/TableContainer"
import Select from "react-select/base"
import { Dropdown } from "../../../shared/components/Dropdown"

const Test = () => {

    const auditData = [
        {
            slNo: 1,
            auditName: 'Audit 1',
            officeName: 'Office A',
            department: 'Finance',
            directorate: 'Directorate X',
            inspectionReport: 'Report 1',
            status: 'Pending'
        },
        {
            slNo: 2,
            auditName: 'Audit 2',
            officeName: 'Office B',
            department: 'HR',
            directorate: 'Directorate Y',
            inspectionReport: 'Report 2',
            status: 'Completed'
        },
        {
            slNo: 3,
            auditName: 'Audit 3',
            officeName: 'Office C',
            department: 'IT',
            directorate: 'Directorate Z',
            inspectionReport: 'Report 3',
            status: 'In Progress'
        },
        // Add more dummy rows as needed
    ];
    const [data, setData] = useState([]);

    useEffect(() => {
        setData(auditData);
    }, []);

    const options = [
        { value: 'Accepted', label: 'Accepted' },
        { value: 'Declined', label: 'Declined' },
        { value: 'Expired', label: 'Expired' },
    ];



    return (
        <React.Fragment>
            <BreadCrumb title='Initiated' pageTitle='Audit Product' />

            <div className="card" id="ordersTable">
                <div className="card-body">
                    <div className="grid grid-cols-1 gap-4 mb-5 lg:grid-cols-2 xl:grid-cols-12">
                        <div className="xl:col-span-3">
                            <div className="relative">
                                <input type="text" className="ltr:pl-8 rtl:pr-8 search form-input border-slate-200 dark:border-zink-500 focus:outline-none focus:border-custom-500 disabled:bg-slate-100 dark:disabled:bg-zink-600 disabled:border-slate-300 dark:disabled:border-zink-500 dark:disabled:text-zink-200 disabled:text-slate-500 dark:text-zink-100 dark:bg-zink-700 dark:focus:border-custom-800 placeholder:text-slate-400 dark:placeholder:text-zink-200" placeholder="Search for ..." autoComplete="off" />
                                <Search className="inline-block size-4 absolute ltr:left-2.5 rtl:right-2.5 top-2.5 text-slate-500 dark:text-zink-200 fill-slate-100 dark:fill-zink-600" />
                            </div>
                        </div>
                        <div className="xl:col-span-2">
                            <Select
                                className="border-slate-200 focus:outline-none focus:border-custom-500"
                                options={options}
                                isSearchable={false} 
                                name="statusFilterSelect"
                                id="statusFilterSelect"
                                placeholder="select department"
                            />
                        </div>
                        <div className="xl:col-span-2">
                            <Select
                                className="border-slate-200 focus:outline-none focus:border-custom-500"
                                options={options}
                                isSearchable={false} 
                                name="statusFilterSelect"
                                id="statusFilterSelect"
                                placeholder="Select FinYear"
                            />
                        </div>
                        <div className="xl:col-span-2 xl:col-start-11">
                            <div className="lg:ltr:text-right lg:rtl:text-left">
                                <Link to="#!" data-modal-target="addEstimateModal" type="buton" className="text-white btn bg-custom-500 border-custom-500 hover:text-white hover:bg-custom-600 hover:border-custom-600 focus:text-white focus:bg-custom-600 focus:border-custom-600 focus:ring focus:ring-custom-100 active:text-white active:bg-custom-600 active:border-custom-600 active:ring active:ring-custom-100 dark:ring-custom-400/20" >
                                    <Plus className="inline-block size-4" /> <span className="align-middle">Add Assets</span>
                                </Link>
                            </div>
                        </div>
                    </div>
                    <div className="-mx-5 overflow-x-auto">
                        <table className="w-full whitespace-nowrap">
                            <thead className="ltr:text-left rtl:text-right bg-slate-100 text-slate-500 dark:text-zink-200 dark:bg-zink-600">
                                <tr>
                                    <th className="px-3.5 py-2.5 font-semibold border border-slate-200 dark:border-zink-500">SL No</th>
                                    <th className="px-3.5 py-2.5 font-semibold border border-slate-200 dark:border-zink-500">Audit Name</th>
                                    <th className="px-3.5 py-2.5 font-semibold border border-slate-200 dark:border-zink-500">Office Name</th>
                                    <th className="px-3.5 py-2.5 font-semibold border border-slate-200 dark:border-zink-500">Department</th>
                                    <th className="px-3.5 py-2.5 font-semibold border border-slate-200 dark:border-zink-500">Directorate</th>
                                    <th className="px-3.5 py-2.5 font-semibold border border-slate-200 dark:border-zink-500">Inspection Report</th>
                                    <th className="px-3.5 py-2.5 font-semibold border border-slate-200 dark:border-zink-500">Status</th>
                                </tr>
                            </thead>
                            <tbody className="">

                                {data.map((item, index) => (
                                    <tr key={index}>
                                        <td className="px-3.5 py-2.5 border border-slate-200 dark:border-zink-500">{item.slNo}</td>
                                        <td className="px-3.5 py-2.5 border border-slate-200 dark:border-zink-500">{item.auditName}</td>
                                        <td className="px-3.5 py-2.5 border border-slate-200 dark:border-zink-500">{item.officeName}</td>
                                        <td className="px-3.5 py-2.5 border border-slate-200 dark:border-zink-500">{item.department}</td>
                                        <td className="px-3.5 py-2.5 border border-slate-200 dark:border-zink-500">{item.directorate}</td>
                                        <td className="px-3.5 py-2.5 border border-slate-200 dark:border-zink-500">{item.inspectionReport}</td>
                                        <td className="px-3.5 py-2.5 border border-slate-200 dark:border-zink-500">{item.status}</td>
                                    </tr>
                                ))}


                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </React.Fragment>
    )
}

export default Test