import { Check, Edit, Mail, NotebookTabs, Send } from "lucide-react";
import React, { useEffect, useState } from "react";
import { getAuditHistory } from "../services/auditService";
import HistorySkeletonLoader from "../../../shared/components/Loading/HistorySkeletonLoader";


const History = ({ inspectionData }) => {
  const [loading, setLoading] = useState(false);
  const [history, setHistory] = useState([]);

  const testData = [
    {
      "status": "CREATED",
      "name": "Shri. KS Gopinath Narayan",
      "designation": "Accountant General",
      "date": "2024-10-05T14:52:04Z"
    },
    {
      "status": "UPDATED",
      "name": "Shri. KS Gopinath Narayan",
      "designation": "Accountant General",
      "date": "2024-10-11T11:22:41Z"
    },
    {
      "status": "SUBMITTED",
      "name": "Shri. Anup Sasi",
      "designation": "Accountant General",
      "date": "2024-10-11T11:22:56Z"
    },
    {
      "status": "RESPONSE_SUBMITTED",
      "name": "Anup <PERSON><PERSON>",
      "designation": "HOD",
      "date": "2024-10-11T11:24:01Z"
    },
    {
      "status": "CLOSED",
      "name": "Shri. KS Gopinath Narayan",
      "designation": "Accountant General",
      "date": "2024-10-11T14:32:51Z"
    }
  ]
  const statusIconMap = {
    Created: { icon: NotebookTabs, color: "text-custom-500" },
    Updated: { icon: Edit, color: "text-red-500" },
    Submitted: { icon: Send, color: "text-green-500" },
    Replayed: { icon: Send, color: "text-purple-500" },
    Queried: { icon: Mail, color: "text-fuchsia-500" },
    Closed: { icon: Check, color: "text-green-500" },
  };



  useEffect(() => {
    try {
      const historyResponse = async () => {
        setLoading(true)
        const response = await getAuditHistory(inspectionData.id);
        setHistory(response || []);
        console.log("historyResponse", response);
      }
      historyResponse();
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false)
    }
  }
    , [inspectionData.id])

  if (loading) { return <HistorySkeletonLoader /> }

  return (
    <React.Fragment>
      {/* <h6 className="mb-4 text-15">Audit History</h6> */}
      <div>
        {history.map((item, index) => {
          const { status, name, designation, date } = item;
          const IconComponent = statusIconMap[status]?.icon || NotebookTabs;
          const colorClass = statusIconMap[status]?.color || "text-gray-500";

          return (
            <div
              key={index}
              className="relative before:absolute ltr:before:border-l-2 rtl:before:border-r-2 ltr:before:left-3.5 rtl:before:right-3.5 before:top-1.5 before:-bottom-1.5 pb-4 dark:before:border-zink-500"
            >
              <div className="relative flex gap-2">
                <div
                  className={`size-8 p-0.5 bg-white ${colorClass} flex items-center justify-center border rounded-full shrink-0 border-slate-200 dark:border-zink-500 dark:bg-zink-700`}
                >
                  <IconComponent className="size-4" />
                </div>
                <div>
                  <h6 className="mb-1">{status.replace(/_/g, " ")} By {designation}</h6>
                  <p className="mb-2 text-slate-500 dark:text-zink-200">{name}</p>
                  <p className="text-sm text-slate-500 dark:text-zink-200">{date}</p>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </React.Fragment>
  );
};

export default History;
