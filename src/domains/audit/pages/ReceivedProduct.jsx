import  React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { SkeletonLoaderBase } from '../../../shared/components/Loading/SkeletonLoaderBase';
import { Download, DownloadIcon, Eye, Link, Search } from 'lucide-react';
import { Pagination } from 'swiper/modules';
import BreadCrumb from '../../../shared/BreadCrumb';
import { receivedAuditProduct } from '../redux/slices/productActions';
import { setPage , filterFinYear} from '../redux/slices/productSlice';
import FinancialYearSelect from '../../../shared/components/general/FinancialYearSelect';

const ReceivedProduct = () => {
    const dispatch = useDispatch();
    const { t } = useTranslation();

    const { receivedProduct, loading, error } = useSelector((state) => state.Product);

    const { officeId } = useSelector((state) => state.User?.userDetails);

    useEffect(() => {
        if(officeId){
            dispatch(receivedAuditProduct({ page: receivedProduct.currentPage - 1, size: 10, seatId:officeId,finYear:receivedProduct.finYear }))
        }
    }, [dispatch, receivedProduct.currentPage, receivedProduct.finYear,officeId])

     const handleYearChange = (year) => {
            const type = "RC"
            dispatch(filterFinYear({type, payload:year}))
        };

    const handlePageChange = (page) => {
        dispatch(setPage(page));
    };

    if (loading) return <SkeletonLoaderBase showBreadcrumb='true' />;
    return (
        <React.Fragment>
            <BreadCrumb title='Received' pageTitle='Audit Product' />

            <div className="grid grid-cols-1 gap-x-5 xl:grid-cols-12">
                <div className="xl:col-span-12">
                    <div className="card" id="usersTable">
                        <div className="card-body">
                            <div className="flex items-center">
                                <h6 className="text-15 grow">{t('receivedInspectionReport')}</h6>
                                {/* <div className="shrink-0">
                                    <button type="button" className="text-white btn bg-custom-500 border-custom-500 hover:text-white hover:bg-custom-600 hover:border-custom-600 focus:text-white focus:bg-custom-600 focus:border-custom-600 focus:ring focus:ring-custom-100 active:text-white active:bg-custom-600 active:border-custom-600 active:ring active:ring-custom-100 dark:ring-custom-400/20" ><Plus className="inline-block size-4" /> <span className="align-middle">Add Auidt Product </span></button>
                                </div> */}
                            </div>
                        </div>
                        <div className="!py-3.5 card-body border-y border-dashed border-slate-200 dark:border-zink-500">
                            <form action="#!">
                                <div className="grid grid-cols-1 gap-5 xl:grid-cols-12">
                                    <div className="relative xl:col-span-2">
                                        <input type="text" className="ltr:pl-8 rtl:pr-8 search form-input border-slate-200 dark:border-zink-500 focus:outline-none focus:border-custom-500 disabled:bg-slate-100 dark:disabled:bg-zink-600 disabled:border-slate-300 dark:disabled:border-zink-500 dark:disabled:text-zink-200 disabled:text-slate-500 dark:text-zink-100 dark:bg-zink-700 dark:focus:border-custom-800 placeholder:text-slate-400 dark:placeholder:text-zink-200" placeholder="Type here..." autoComplete="off" />
                                        <Search className="inline-block size-4 absolute ltr:left-2.5 rtl:right-2.5 top-2.5 text-slate-500 dark:text-zink-200 fill-slate-100 dark:fill-zink-600" />
                                    </div>
                                    <div className="xl:col-span-2">
                                    <FinancialYearSelect
                                            numYears={5}
                                            onChange={handleYearChange}
                                            isRequired={true} 
                                            selectedYear={receivedProduct.finYear}/>
                                    </div>
                                    <div className="xl:col-span-3 xl:col-start-10">
                                        <div className="flex gap-2 xl:justify-end">
                                            <div>
                                                <button type="button" className="bg-white border-dashed text-custom-500 btn border-custom-500 hover:text-custom-500 hover:bg-custom-50 hover:border-custom-600 focus:text-custom-600 focus:bg-custom-50 focus:border-custom-600 active:text-custom-600 active:bg-custom-50 active:border-custom-600 dark:bg-zink-700 dark:ring-custom-400/20 dark:hover:bg-custom-800/20 dark:focus:bg-custom-800/20 dark:active:bg-custom-800/20"><Download className="inline-block size-4" /> <span className="align-middle">Download Pdf</span></button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <div className="card-body">
                            {receivedProduct.repliedData && receivedProduct.repliedData.length > 0 ?
                                <div className="-mx-5 overflow-x-auto">
                                    <table className="w-full whitespace-nowrap">
                                        <thead className="ltr:text-left rtl:text-right bg-slate-100 text-slate-500 dark:text-zink-200 dark:bg-zink-600">
                                            <tr>
                                            <th className="px-3.5 py-2.5 font-semibold border border-slate-200 dark:border-zink-500">{t('slNo')}</th>
                                                <th className="px-3.5 py-2.5 font-semibold border border-slate-200 dark:border-zink-500">{t('auditName')}</th>
                                                <th className="px-3.5 py-2.5 font-semibold border border-slate-200 dark:border-zink-500">{t('department')}</th>
                                                <th className="px-3.5 py-2.5 font-semibold border border-slate-200 dark:border-zink-500">{t('directorate')}</th>
                                                <th className="px-3.5 py-2.5 font-semibold border border-slate-200 dark:border-zink-500">{t('inspectionReport')}</th>
                                                <th className="px-3.5 py-2.5 font-semibold border border-slate-200 dark:border-zink-500">{t('repliedDate')}</th>
                                                <th className="px-3.5 py-2.5 font-semibold border border-slate-200 dark:border-zink-500"></th>
                                            </tr>
                                        </thead>
                                        <tbody className="">

                                            {receivedProduct.repliedData.map((item, index) => (
                                                <tr key={item.id}>
                                                    <td className="px-3.5 py-2.5 border border-slate-200 dark:border-zink-500">{index+1}</td>
                                                    <td className="px-3.5 py-2.5 border border-slate-200 dark:border-zink-500">{item.auditName}</td>
                                                    <td className="px-3.5 py-2.5 border border-slate-200 dark:border-zink-500">{item.departmentName}</td>
                                                    <td className="px-3.5 py-2.5 border border-slate-200 dark:border-zink-500">{item.directorateName}</td>
                                                    <td className="px-3.5 py-2.5 border border-slate-200 dark:border-zink-500 text-center">
                                                        <div className="flex items-center justify-center">
                                                            <Link to="#!" className="flex items-center justify-center size-8 transition-all duration-200 ease-linear rounded-md text-slate-500 bg-slate-100 hover:text-white hover:bg-slate-500 dark:bg-zink-600 dark:text-zink-200 dark:hover:text-white dark:hover:bg-zink-500">
                                                                <DownloadIcon className="size-4" />
                                                            </Link>
                                                        </div>
                                                    </td>
                                                    <td className="px-3.5 py-2.5 border border-slate-200 dark:border-zink-500">{item.status}</td>
                                                    <td className="px-3.5 py-2.5 border border-slate-200 dark:border-zink-500">
                                                        <div className="flex gap-4 items-center justify-center">
                                                        <Link to="#!" className="flex items-center justify-center size-8 text-red-500 transition-all duration-200 ease-linear bg-red-100 rounded-md hover:text-white hover:bg-red-500 dark:bg-red-500/20 dark:hover:bg-red-500" ><Download className="size-4" /></Link>
                                                            <Link to="#!" className="flex items-center justify-center size-8 text-green-500 transition-all duration-200 ease-linear bg-green-100 rounded-md hover:text-white hover:bg-green-500 dark:bg-green-500/20 dark:hover:bg-green-500"><Eye className="size-4" /></Link>
                                                        </div>
                                                    </td>
                                                </tr>
                                            ))}


                                        </tbody>
                                    </table>
                                    <Pagination
                                        currentPage={receivedProduct.currentPage}
                                        totalPages={receivedProduct.totalPages}
                                        totalRecords={receivedProduct.totalElements}
                                        onPageChange={handlePageChange}
                                    />
                                </div>
                                :
                                (<div className="noresult">
                                    <div className="py-6 text-center">
                                        <Search className="size-6 mx-auto text-sky-500 fill-sky-100 dark:sky-500/20" />
                                        <h5 className="mt-2 mb-1">No Result Found</h5>
                                        <p className="mb-0 text-slate-500 dark:text-zink-200">In this Financial Year there is no submitted Inspection report found.</p>
                                    </div>
                                </div>)}

                               
                        </div>
                    </div>
                </div>
            </div>
        </React.Fragment>
    );
}

export default ReceivedProduct;
