import { ViewItem } from "../../../shared/components/form/ViewItem";
import Query from "./Query";

const ViewObservation = ({ data }) => {
  console.log(data, 'data observation');
  return (
    <div className="p-4 border border-sky-800 rounded-lg shadow-lg">
      <div className="flex justify-between items-center pb-4 border-b border-gray-300">
        <h2 className="text-2xl font-bold text-sky-800">Observation Details</h2>
      </div>
      <div className="mt-4 grid grid-cols-1 lg:grid-cols-12 gap-4">
        <div className="lg:col-span-6">
          <ViewItem label="Observation Number" value={data?.observationNo || "N/A"} />
        </div>
        <div className="lg:col-span-6">
          <ViewItem label="Observation Title" value={data?.observationTitle || "N/A"} />
        </div>
        <div className="lg:col-span-6">
          <ViewItem label="Attachment Name" value={data?.attachmentName || "N/A"} />
        </div>
        <div className="lg:col-span-6">
          <ViewItem label="Attachment" value={data?.attachment || "N/A"} />
        </div>
        <div className="lg:col-span-12">
          <ViewItem
            label="Details Description"
            value={data?.description || "N/A"}
          />
        </div>
      </div>
      {/* <Query /> */}
    </div>
  );
};

export default ViewObservation;
