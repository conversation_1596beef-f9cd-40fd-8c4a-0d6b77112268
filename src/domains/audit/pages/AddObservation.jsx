import React, { useCallback, useState } from 'react';
import Modal from '../../../shared/components/modal';
import { useForm } from 'react-hook-form';
import TextField from '../../../shared/components/form/TextField';
import { Loader2 } from 'lucide-react';
import { toast, ToastContainer } from 'react-toastify';
import NoteSkeletonLoader from '../../../shared/components/Loading/NoteSkeletonLoader';
import {saveAuditObservation} from '../services/auditService';

const AddObservation = ({ inspectionData,onReloadObs}) => {
    const [show, setShow] = useState(false);
    const [isProcessing, setIsProcessing] = useState(false);
    const {
        register,
        handleSubmit,
        formState: { errors },
    } = useForm();
    const toggle = useCallback(() => setShow(prev => !prev), []);


    const onSubmit = async (data) => {
        try {
            setIsProcessing(true);
            const modifiedData = {
                ...data,
                inspectionReportId: inspectionData.id,
            }
            const response = await saveAuditObservation(modifiedData);
            toast.success("Data saved successfully!", {
                icon: "✔️",
                theme: "colored",
                position: "top-right",
                autoClose: 3000, // Close after 3 seconds
            });
            onReloadObs();
        } catch (error) {
            console.log(error)
            toast.error("Something went wrong!", {
                icon: "❌",
                theme: "colored",
                position: "top-right",
                autoClose: 3000, // Close after 3 seconds
            });
        } finally {
            setIsProcessing(false);
            toggle();
        }
    }

    const validateFile = (fileList) => {
        const file = fileList[0];
        if (file.size > 5000000) { // 5MB
            return "File size must be less than 5MB";
        }
        if (!["application/pdf", "image/jpeg", "image/png"].includes(file.type)) {
            return "Only PDF, JPEG, and PNG files are allowed";
        }
        return true;
    };
    return (
        <>
            <button
                type="button" onClick={toggle}
                className="w-full sm:w-auto bg-white border-dashed text-sky-500 btn border-sky-500 hover:text-sky-500 hover:bg-sky-50 hover:border-sky-600 focus:text-sky-600 focus:bg-sky-50 focus:border-sky-600 active:text-sky-600 active:bg-sky-50 active:border-sky-600 dark:bg-zinc-700 dark:ring-sky-400/20 dark:hover:bg-sky-800/20 dark:focus:bg-sky-800/20 dark:active:bg-sky-800/20 text-xs sm:text-sm px-4 sm:px-6 py-2 sm:py-3"
            >
                <i className="align-baseline ltr:pr-1 rtl:pl-1 ri-add-line"></i>Observation
            </button>
            <ToastContainer />





            <Modal show={show} onHide={toggle} modal-center="true"
                className="fixed flex flex-col transition-all duration-300 ease-in-out left-2/4 z-drawer -translate-x-2/4 -translate-y-2/4"
                dialogClassName="w-screen lg:w-[55rem] bg-white shadow rounded-md dark:bg-zink-600">
                <Modal.Header className="flex items-center justify-between p-4 border-b dark:border-zink-500"
                    closeButtonClass="transition-all duration-200 ease-linear text-slate-400 hover:text-red-500">
                    <Modal.Title className="text-16"> Add Observation</Modal.Title>
                </Modal.Header>
                <Modal.Body className="max-h-[calc(theme('height.screen')_-_180px)] p-4 overflow-y-auto">
                    <form onSubmit={handleSubmit(onSubmit)}>
                        <div className="grid grid-cols-1 gap-5 xl:grid-cols-12 mt-8">
                            <div className="xl:col-span-6">
                                <TextField
                                    label="Observation Number"
                                    name="observationNo"
                                    register={register}
                                    validation={{ required: "Observation Number required" }}
                                    error={errors?.observationNo}
                                    placeholder="Observation Number"
                                    labelClass="font-bold text-sky-800"
                                />
                            </div>
                            <div className="xl:col-span-6">
                                <TextField
                                    label="Observation Title"
                                    name="observationTitle"
                                    register={register}
                                    validation={{ required: "Observation Title required" }}
                                    error={errors?.observationTitle}
                                    placeholder="Observation Title"
                                    labelClass="font-bold text-sky-800"
                                />
                            </div>

                            {/* <div className="xl:col-span-6">
                                <label htmlFor="inputValue" className="inline-block mb-2 font-bold text-sky-800">Attachment</label>
                                <div className="flex items-center border border-slate-300 dark:border-zinc-500 rounded-md overflow-hidden w-full">
                                    <input
                                        type="file"
                                        {...register("file",
                                            { validate: validateFile })}
                                        className="block w-full text-sm text-slate-500 dark:text-zinc-400
                                                    file:py-2 file:px-4 
                                                    file:rounded-none file:border-0 
                                                    file:text-sm file:font-semibold 
                                                    file:bg-slate-100 file:text-slate-700 
                                                    hover:file:bg-slate-200 
                                                    dark:file:bg-zinc-600 dark:file:text-zinc-300 dark:hover:file:bg-zinc-500"
                                    />
                                    {errors.file && (
                                        <p style={{ color: "red", fontSize: "12px" }}>{errors.file.message}</p>
                                    )}
                                </div>
                            </div> */}
                            <div className="xl:col-span-12">
                                <TextField
                                    label="Details Description"
                                    name="description"
                                    register={register}
                                    validation={{ required: "Description is required" }}
                                    error={errors.description}
                                    placeholder="Details Description"
                                    labelClass="font-bold text-sky-800"
                                    multiline
                                />
                            </div>
                        </div>
                        <div className="flex justify-end mt-6 gap-x-4">
                            <button
                                type="submit"
                                onClick={handleSubmit}
                                className={`text-white btn bg-custom-500 border-custom-500 hover:text-white hover:bg-custom-600 hover:border-custom-600 focus:text-white focus:bg-custom-600 focus:border-custom-600 focus:ring focus:ring-custom-100 active:text-white active:bg-custom-600 active:border-custom-600 active:ring active:ring-custom-100 dark:ring-custom-400/20 ${isProcessing ? "cursor-not-allowed opacity-70" : ""
                                    }`}
                                disabled={isProcessing}
                            >
                                {isProcessing ? (
                                    <span className="flex items-center">
                                        <Loader2 className="size-4 ltr:mr-2 rtl:ml-2 animate-spin" />
                                        Saving...
                                    </span>
                                ) : (
                                    <span>Save</span>
                                )}
                            </button>
                        </div>
                    </form>
                </Modal.Body>
            </Modal>
        </>
    );
}

export default AddObservation;
