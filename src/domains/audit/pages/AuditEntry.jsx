import { <PERSON><PERSON><PERSON><PERSON>, Loader2 } from "lucide-react"
import React, { useEffect, useState } from "react"
import Flatpickr from 'react-flatpickr';
import "flatpickr/dist/flatpickr.min.css";
import Select from "react-select";
import { useGetDepartmentsQuery,useGetDirectoratesQuery } from '../../core/redux/api/departmentApi'
import { useGetAuditTypeQuery, useGetAuditSubTypeQuery } from '../../core/redux/api/auditTypeSubTypeApi'
import FinancialYearSelect from "../../../shared/components/general/FinancialYearSelect";
import { Controller, useForm } from "react-hook-form";
import { getInspectionReportData, saveOrUpdateInspectionReport } from '../services/auditService';
import SkeletonLoader from "../../../shared/components/Loading/SkeletonLoader";
import { toast, ToastContainer } from "react-toastify";


  
const AuditEntry = ({id, gridClasses='grid grid-cols-1 gap-5 xl:grid-cols-12 mt-8',gridSpan='xl:col-span-4'}) => {
    const {
        register,
        handleSubmit,
        control,
        reset,
        formState: { errors },
        watch,
        setValue
    } = useForm();
    const [formData, setFormData] = useState(null)
    // const location = useLocation();
    // const queryParams = new URLSearchParams(location.search);
    // const id = queryParams.get('id');
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const  allotId  = localStorage.getItem('allotId')
    

    const { data: departments, isLoading: isDepartmentsLoading } = useGetDepartmentsQuery();
    const [selectedDeptId, setSelectedDeptId] = useState(null);
    const { data: directorates, isLoading: isDirectoratesLoading } = useGetDirectoratesQuery(selectedDeptId, {
        skip: !selectedDeptId, 
    });
    const departmentOptions = departments? departments.map(dept => ({
            value: dept.hierarchyId,
            label: dept.hierarchyName,
        }))
        : [];
    
        const directorateOptions = directorates? directorates.map(dir => ({
            value: dir.hierarchyId,
            label: dir.hierarchyName,
        }))
        : [];


    const { data: auditTypes, isLoading: isAuditTypesLoading } = useGetAuditTypeQuery();
    const [selectedAuditTypeId, setSelectedAuditTypeId] = useState(null);

    const { data: auditSubTypes, isLoading: isAuditSubTypesLoading } = useGetAuditSubTypeQuery(selectedAuditTypeId, {
        skip: !selectedAuditTypeId, 
    });

    const auditTypeOptions = auditTypes?.map((type) => ({
        value: type.id,
        label: type.typeName,
    })) || [];

    const auditSubTypeOptions = auditSubTypes?.map((subType) => ({
        value: subType.id,
        label: subType.auditSubType,
    })) || [];

    useEffect(() => {
        if (id) {
            setLoading(true);
            getInspectionReportData(id)
                .then((response) => {
                    const data = response;
                    setFormData(data);
                    setSelectedAuditTypeId(response.typeSetup.id)
                    setSelectedDeptId(response.deptId)
                    Object.keys(data).forEach((key) => setValue(key, data[key]));
                })
                .catch((error) => setError("Error fetching data",error))
                .finally(() => setLoading(false));
        }
    }, [id, setValue])


    //for testing 
    const handleYearChange = (year) => {
        console.log('Selected Financial Year from Form:', year);
    };

    const [isProcessing, setIsProcessing] = useState(false);

    const onSubmit = async (data) => {
        try {
            setLoading(true);
            setIsProcessing(true);
            const formattedId = formData?.id ? parseInt(formData.id, 10) : null;
            const response = await saveOrUpdateInspectionReport({
                ...data,
                id: formattedId,
                createdBy: allotId
            });
            setFormData(response)
            reset(response);
            toast.success("Data saved successfully!", {
                icon: "✔️", 
                theme: "colored",
                position: "top-right",
                autoClose: 3000, // Close after 3 seconds
              });
        } catch (error) {
            setError("Error saving data",error);
            toast.error("Something went wrong!", {
                icon: "❌", 
                theme: "colored",
                position: "top-right",
                autoClose: 3000, // Close after 3 seconds
              });
        }finally{
            setIsProcessing(false);
            setLoading(false);
        }
    }
    const handleClear = () => {
        reset();
        toast.info("Fields cleared!", {
            icon: "ℹ️", 
            theme: "colored",
            position: "top-right",
            autoClose: 3000,
        });
    }

    const validateDates = (value) => {
        const fromDate = watch("auditFromDate");
        if (fromDate && new Date(value) < new Date(fromDate)) {
            return "To date must be later than from date";
        }
        return true;
    };

    const validateFile = (fileList) => {
        const file = fileList[0];
        if (file.size > 5000000) { // 5MB
            return "File size must be less than 5MB";
        }
        if (!["application/pdf", "image/jpeg", "image/png"].includes(file.type)) {
            return "Only PDF, JPEG, and PNG files are allowed";
        }
        return true;
    };

    if(loading) return <SkeletonLoader count={9} gridClasses="grid grid-cols-1 gap-5 xl:grid-cols-12 mt-8"/>

    return (
        <React.Fragment>
            <form onSubmit={handleSubmit(onSubmit)}>
                <div className={gridClasses}>
                    <div className={gridSpan}>
                        <label htmlFor="inputValue" className="inline-block mb-2 text-base font-medium">Audit Type</label>
                        <Controller
                            name="typeSetup.id"
                            control={control}
                            rules={{ required: "Audit type is required" }}
                            render={({ field }) => (
                                <Select
                                    {...field}
                                    options={auditTypeOptions}
                                    isSearchable={true}
                                    placeholder="Select an audit type"
                                    isLoading={isAuditTypesLoading}
                                    value={auditTypeOptions?.find(option => option.value === field.value)} 
                                    onChange={(option) => {
                                        setSelectedAuditTypeId(option?.value || null);
                                        field.onChange(option?.value);
                                    }}
                                />
                            )} />
                        {errors?.typeSetup?.id && (
                            <p style={{ color: "red", fontSize: "12px" }}>{errors.typeSetup.id.message}</p>
                        )}
                    </div>
                    <div className={gridSpan}>
                        <label htmlFor="inputValue" className="inline-block mb-2 text-base font-medium">Audit Sub Type</label>
                        <Controller
                            name="auditSubTypeSetup.id"
                            control={control}
                            rules={{ required: "Audit Sub Type is required" }}
                            render={({ field }) => (
                                <Select
                                    {...field}
                                    options={auditSubTypeOptions}
                                    isSearchable={true}
                                    isLoading={isAuditSubTypesLoading}
                                    isDisabled={!selectedAuditTypeId}
                                    placeholder="Select an audit sub type"
                                    value={auditSubTypeOptions?.find(option => option.value === field.value)}
                                    onChange={(selectedOption) => field.onChange(selectedOption.value)}
                                />
                            )} />
                        {errors?.auditSubTypeSetup?.id && (
                            <p style={{ color: "red", fontSize: "12px" }}>{errors.auditSubTypeSetup.id.message}</p>
                        )}
                    </div>
                    <div className={gridSpan}>
                        <label htmlFor="inputValue" className="inline-block mb-2 text-base font-medium">Audit Name</label>
                        <input
                            placeholder="Enter Audit Name"
                            {...register("auditName",
                                { required: "Audit name is required" },
                            )}
                            className="form-input border-slate-200 dark:border-zink-500 focus:outline-none focus:border-custom-500 disabled:bg-slate-100 dark:disabled:bg-zink-600 disabled:border-slate-300 dark:disabled:border-zink-500 dark:disabled:text-zink-200 disabled:text-slate-500 dark:text-zink-100 dark:bg-zink-700 dark:focus:border-custom-800 placeholder:text-slate-400 dark:placeholder:text-zink-200" />
                        {errors.auditName && (
                            <p style={{ color: "red", fontSize: "12px" }}>{errors.auditName.message}</p>
                        )}
                    </div>
                    <div className={gridSpan}>
                        <label htmlFor="inputValue" className="inline-block mb-2 text-base font-medium">Financial Year</label>
                        <FinancialYearSelect
                            numYears={5}
                            onChange={handleYearChange}
                            control={control}
                            isRequired={true} />
                        {errors.finYear && (
                            <p style={{ color: "red", fontSize: "12px" }}>{errors.finYear.message}</p>
                        )}
                    </div>

                    <div className={gridSpan}>
                        <label htmlFor="joiningDateInput" className="inline-block mb-2 text-base font-medium">Audit from Date</label>
                        <div className="relative">
                            <CalendarRange className="absolute size-4 ltr:left-3 rtl:right-3 top-3 text-slate-500 dark:text-zink-200" />
                            <Controller
                                name="auditFromDate"
                                control={control}
                                rules={{ required: "Audit From Date is required" }}
                                render={({ field }) => (
                                    <Flatpickr
                                        {...field}
                                        options={{ dateFormat: "Y-m-d" }}
                                        placeholder='Select from date'
                                        onChange={(selectedDates) => {
                                            field.onChange(selectedDates[0]); 
                                        }}
                                        className="ltr:pl-10 rtl:pr-10 form-input border-slate-200 dark:border-zink-500 focus:outline-none focus:border-custom-500 disabled:bg-slate-100 dark:disabled:bg-zink-600 disabled:border-slate-300 dark:disabled:border-zink-500 dark:disabled:text-zink-200 disabled:text-slate-500 dark:text-zink-100 dark:bg-zink-700 dark:focus:border-custom-800 placeholder:text-slate-400 dark:placeholder:text-zink-200"
                                    />
                                )} />
                            {errors.auditFromDate && (
                                <p style={{ color: "red", fontSize: "12px" }}>{errors.auditFromDate.message}</p>
                            )}
                        </div>
                    </div>
                    <div className={gridSpan}>
                        <label htmlFor="joiningDateInput" className="inline-block mb-2 text-base font-medium">Audit to Date</label>
                        <div className="relative">
                            <CalendarRange className="absolute size-4 ltr:left-3 rtl:right-3 top-3 text-slate-500 dark:text-zink-200" />
                            <Controller
                                name="auditToDate"
                                control={control}
                                rules={{ required: "Audit To Date is required", validate: validateDates }}
                                render={({ field }) => (
                                    <Flatpickr
                                        {...field}
                                        options={{ dateFormat: "Y-m-d" }}
                                        onChange={(selectedDates) => {
                                            field.onChange(selectedDates[0]); 
                                        }}
                                        placeholder='Select to date'
                                        className="ltr:pl-10 rtl:pr-10 form-input border-slate-200 dark:border-zink-500 focus:outline-none focus:border-custom-500 disabled:bg-slate-100 dark:disabled:bg-zink-600 disabled:border-slate-300 dark:disabled:border-zink-500 dark:disabled:text-zink-200 disabled:text-slate-500 dark:text-zink-100 dark:bg-zink-700 dark:focus:border-custom-800 placeholder:text-slate-400 dark:placeholder:text-zink-200"
                                    />
                                )} />
                            {errors.auditToDate && (
                                <p style={{ color: "red", fontSize: "12px" }}>{errors.auditToDate.message}</p>
                            )}
                        </div>
                    </div>
                    <div className={gridSpan}>
                        <label htmlFor="inputValue" className="inline-block mb-2 text-base font-medium">Inspection Report</label>
                        <input
                            type="file"
                            {...register("inspectionReport",
                                { required: "Choose Inspection Report", validate: validateFile })}
                            className="cursor-pointer form-file border-slate-200 dark:border-zink-500 focus:outline-none focus:border-custom-500"
                        />
                        {errors.inspectionReport && (
                            <p style={{ color: "red", fontSize: "12px" }}>{errors.inspectionReport.message}</p>
                        )}
                    </div>
                    <div className={gridSpan}>
                        <label htmlFor="inputValue" className="inline-block mb-2 text-base font-medium">Department</label>
                        <Controller
                            name="deptId"
                            control={control}
                            rules={{ required: "Department is required" }}
                            render={({ field }) => (
                                <Select
                                    {...field}
                                    name="choices-single-no-sorting"
                                    options={departmentOptions}
                                    isSearchable={true}
                                    isLoading={isDepartmentsLoading}
                                    placeholder="Select a department"
                                    value={departmentOptions?.find(option => option.value === field.value)}
                                    onChange={(option) => {
                                        setSelectedDeptId(option?.value || null)
                                        field.onChange(option.value)
                                    }}
                                />


                            )}
                        />
                        {errors.deptId && (
                            <p style={{ color: "red", fontSize: "12px" }}>{errors.deptId.message}</p>
                        )}

                    </div>
                    <div className={gridSpan}>
                        <label htmlFor="inputValue" className="inline-block mb-2 text-base font-medium">Directorates</label>
                        <Controller
                            name="dirId"
                            control={control}
                            render={({ field }) => (
                                <Select
                                    {...field}
                                    name="choices-single-no-sorting"
                                    options={directorateOptions}
                                    isSearchable={true}
                                    isLoading={isDirectoratesLoading}
                                    isDisabled={!selectedDeptId}
                                    placeholder="Select a directorate"
                                    value={directorateOptions?.find(option => option.value === field.value)}
                                    onChange={(selectedOption) => field.onChange(selectedOption.value)}
                                />
                            )} />
                    </div>
                </div>
                <div className="flex justify-end mt-6 gap-x-4">
                    <button
                        type="submit"
                        onClick={handleSubmit}
                        className={`text-white btn bg-custom-500 border-custom-500 hover:text-white hover:bg-custom-600 hover:border-custom-600 focus:text-white focus:bg-custom-600 focus:border-custom-600 focus:ring focus:ring-custom-100 active:text-white active:bg-custom-600 active:border-custom-600 active:ring active:ring-custom-100 dark:ring-custom-400/20 ${isProcessing ? "cursor-not-allowed opacity-70" : ""
                            }`}
                        disabled={isProcessing}
                    >
                        {isProcessing ? (
                            <span className="flex items-center">
                                <Loader2 className="size-4 ltr:mr-2 rtl:ml-2 animate-spin" />
                                Processing...
                            </span>
                        ) : (
                            <span>{id > 0 ? "Update" : "Initiate"}</span>
                        )}
                    </button>
                    {!isProcessing && id == null && (
                        <button
                            type="button"
                            onClick={handleClear}
                            className="text-red-500 bg-red-100 btn hover:text-white hover:bg-red-600 focus:text-white focus:bg-red-600 focus:ring focus:ring-red-100 active:text-white active:bg-red-600 active:ring active:ring-red-100 dark:bg-red-500/20 dark:text-red-500 dark:hover:bg-red-500 dark:hover:text-white dark:focus:bg-red-500 dark:focus:text-white dark:active:bg-red-500 dark:active:text-white dark:ring-red-400/20"
                        >
                            Clear
                        </button>
                    )}
                </div>
            </form>
            <ToastContainer />
        </React.Fragment>
    )
}

export default AuditEntry