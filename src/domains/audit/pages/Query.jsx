import { Paperclip, Search, SendHorizonal } from 'lucide-react'
import SimpleBar from 'simplebar-react'
import QueryMessage from './QueryMessage';
const Query = () => {
    const messages = [
        {
            sender: 'Accountant General',
            senderName: 'Shri. KS Gopinath Narayan',
            senderLink: '#!',
            time: 'Aug 6, 2023, 9:04PM',
            message: [
                'Hi,',
                'Custom software solutions are tailor-made software applications designed to meet the unique needs of a specific business or organization. Unlike off-the-shelf software, which offers a standardized solution for a broad range of users, custom software is precisely crafted to align with the workflows, processes, and objectives of a particular business.',
                'The key advantage of custom software lies in its ability to be scalable and flexible. It can evolve alongside the business, accommodating changing requirements and supporting expansion. By adapting to the specific needs of the organization, custom software empowers businesses to gain a competitive edge, differentiate themselves in the market, and deliver enhanced experiences to their customers.',
                'Thank You',
            ],
        },
        {
            sender: 'HOD',
            senderName: '<PERSON><PERSON>',
            senderLink: '#!',
            time: '07 Nov, 2023, 10:14PM',
            message: [
                'Hi,',
                'I hope this email finds you well. Let me start by saying that I am a big fan of your work and it has inspired me to push myself beyond what I thought were my limits!',
                'After taking a good look at [target company], I realize that you could improve in [improvement area]. I have helped many others improve in the same area and I’d be more than happy to talk with you about it!',
                'Would you be available for a quick call to discuss how our [product/service] could help you?',
                'Regards, Themesdesign',
            ],
        },
        {
            sender: 'Accountant General',
            senderName: 'Shri. KS Gopinath Narayan',
            senderLink: '#!',
            time: '07 Nov, 2023, 10:42PM',
            message: [
                'Hello, Themesdesign',
                'You are probably very busy, I totally understand that!',
                'It would be great to hear back from you. So, please let me know when you find some time.',
            ],
        },
    ];

    return (
        <div id="emailOverview">
            <div className="card-body">
                <div className="flex gap-2">
                    <div className="grow">
                        <h6 className="mb-1 text-16">Query</h6>
                        <p className="text-slate-500 dark:text-zink-200">New Query Received On Aug 6, 2023, 9:04PM</p>
                    </div>
                </div>
            </div>

            <SimpleBar className="xl:max-h-[calc(100vh_-_385px)]">
                <div className="card-body !pt-0">
                {messages && messages.length > 0 ?
                    messages.map((msg, index) => (
                        <QueryMessage key={index} {...msg} />
                    )) :
                    (<div className="noresult">
                        <div className="py-6 text-center">
                            <Search className="size-6 mx-auto text-sky-500 fill-sky-100 dark:sky-500/20" />
                            <h5 className="mt-2 mb-1"> No Query Found</h5>
                            <p className="mb-0 text-slate-500 dark:text-zink-200">No Query Initiated against this Observation</p>
                        </div>
                    </div>)}
                </div>
            </SimpleBar>

            <div className="card-body">
                <div className="flex items-center gap-2">
                    <div className="grow">
                        <input
                            type="text"
                            id="inputText"
                            className="form-input border-slate-200 dark:border-zink-500 focus:outline-none focus:border-custom-500 disabled:bg-slate-100 dark:disabled:bg-zink-600 disabled:border-slate-300 dark:disabled:border-zink-500 dark:disabled:text-zink-200 disabled:text-slate-500 dark:text-zink-100 dark:bg-zink-700 dark:focus:border-custom-800 placeholder:text-slate-400 dark:placeholder:text-zink-200"
                            placeholder="Query"
                            required
                            autoComplete="off"
                        />
                    </div>
                    <div className="flex gap-2 shrink-0">
                        <button
                            type="button"
                            className="flex items-center justify-center size-[37.5px] transition-all duration-200 ease-linear p-0 text-slate-500 btn bg-transparent border-transparent hover:text-slate-700 focus:text-slate-700 active:text-slate-700 dark:text-zink-200 dark:hover:text-zink-50 dark:focus:text-zink-50 dark:active:text-zink-50"
                        >
                            <Paperclip className="size-4" />
                        </button>
                        <button
                            type="button"
                            className="text-white btn bg-custom-500 border-custom-500 hover:text-white hover:bg-custom-600 hover:border-custom-600 focus:text-white focus:bg-custom-600 focus:border-custom-600 focus:ring focus:ring-custom-100 active:text-white active:bg-custom-600 active:border-custom-600 active:ring active:ring-custom-100 dark:ring-custom-400/20"
                        >
                            <SendHorizonal className="inline-block size-4 mr-1 align-middle" />
                            <span className="align-middle"></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Query;