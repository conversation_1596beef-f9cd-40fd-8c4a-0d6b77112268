import React, { lazy, Suspense, useEffect, useState } from "react"
import BreadCrumb from '../../../shared/BreadCrumb'
import { Link, useLocation } from "react-router-dom"
import { ArrowLeftCircle } from "lucide-react";
import { TabLink } from "../../../shared/components/tab/TabLink";
import {getInspectionReportData} from '../services/auditService'
import Loader from "../../../shared/components/Loading/Loader";


const AuditDetails = lazy(() => import("./AuditDetails"));
const Observation = lazy(() => import("./Observation"));
const Notes = lazy(() => import("./Notes"));
const History = lazy(() => import("./History"));
const ViewObservation = lazy(() => import("./ViewObservation"));

const ViewAuditDetails = () => {
    const [activeTab, setActiveTab] = useState(1);
    const toggleTab = (tab, cate) => {
        if (activeTab !== tab) {
            console.log(cate);
            setActiveTab(tab);
        }
    };

    const location = useLocation();
    const queryParams = new URLSearchParams(location.search);
    const inspectionReportId = queryParams.get('id');
    const [inspectionData, setInspectionData] = useState(null); 
    const [isLoading, setIsLoading] = useState(true); 

    useEffect(()=>{
        const fetchInspectionData = async () => {
            setIsLoading(true);
            try {
                const response = await getInspectionReportData(inspectionReportId);
                setInspectionData(response);
            } catch (error) {
                console.error("Error fetching data:", error);
            } finally {
                setIsLoading(false);
            }
        };
        fetchInspectionData();
    }
    ,[inspectionReportId])

    const renderTabContent = () => {
        if (!inspectionData) return null;
        switch (activeTab) {
            case 1:
                return <AuditDetails inspectionData={inspectionData} />;
            case 2:
                return (inspectionData.agSubmittedBy != null ? <ViewObservation /> : <Observation inspectionData={inspectionData} />);
            case 3:
                return <Notes inspectionData={inspectionData} />;
            case 4:
                return <History inspectionData={inspectionData} />;
            default:
                return null;
        }
    };

    return (
        <React.Fragment>
            <BreadCrumb title='Audit Product Deatils' pageTitle='Auidt Product' />
            <div className="card">
                <div className="card-body">
                    <div className="grid grid-cols-1 xl:grid-cols-12 gap-5">
                        <div className="xl:col-span-11">
                            <ul className="flex flex-wrap w-full gap-2 text-sm font-medium text-center filter-btns grow">
                                <TabLink tabId={1} activeTab={activeTab} onClick={toggleTab} label="Audit Details" id={inspectionReportId} />
                                <TabLink tabId={2} activeTab={activeTab} onClick={toggleTab} label="Observation" id={inspectionReportId} />
                                <TabLink tabId={3} activeTab={activeTab} onClick={toggleTab} label="Notes" id={inspectionReportId} />
                                <TabLink tabId={4} activeTab={activeTab} onClick={toggleTab} label="History" id={inspectionReportId} />
                            </ul>
                        </div>
                        <div className="xl:col-start-12 xl:col-span-1">
                            <div className="flex">
                                <div className="shrink-0">
                                    <Link
                                        to="/initiated-product"
                                        className="text-white btn bg-custom-500 border-custom-500 hover:text-white hover:bg-custom-600 hover:border-custom-600 focus:text-white focus:bg-custom-600 focus:border-custom-600 focus:ring focus:ring-custom-100 active:text-white active:bg-custom-600 active:border-custom-600 active:ring active:ring-custom-100 dark:ring-custom-400/20"
                                    >
                                        <ArrowLeftCircle className="inline-block size-4 mr-2" />
                                        <span className="align-middle">Back</span>
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <div className="card">
                <div className="card-body">
                    <div className="grid grid-cols-1 gap-x-5 md:grid-cols-1 xl:grid-cols-1">
                        {isLoading ? (
                            <Loader />
                        ) : (
                            <Suspense fallback={<Loader />} key={activeTab}>
                                {renderTabContent()}
                            </Suspense>
                        )}
                    </div>
                </div>
            </div>
        </React.Fragment>
    )
}

export default ViewAuditDetails
