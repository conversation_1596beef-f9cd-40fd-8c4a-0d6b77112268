import { useCallback, useState } from 'react';
import Modal from '../../../shared/components/modal';
import TextField from '../../../shared/components/form/TextField';
import { useForm } from 'react-hook-form';
import { saveOrUpdateNotes } from '../services/auditService'
import { toast, ToastContainer } from 'react-toastify';
import NoteSkeletonLoader from '../../../shared/components/Loading/NoteSkeletonLoader';
import { Loader2 } from 'lucide-react';

const AddNotes = ({ inspectionData, onReload }) => {
    const [show, setShow] = useState(false);
    const [loading, setLoading] = useState(false);
    const [isProcessing, setIsProcessing] = useState(false);
    const allotId = localStorage.getItem('allotId')
    const {
        register,
        handleSubmit,
        formState: { errors },
    } = useForm();

    const toggle = useCallback(() => setShow(prev => !prev), []);

    const onSubmit = async (data) => {
        setLoading(true);
        setIsProcessing(true);
        try {
            const modifiedData = {
                ...data,
                inspectionReportId: inspectionData.id,
                enteredBy: allotId,
                deleteStatus:'N'

            }
            const response = await saveOrUpdateNotes(modifiedData);
            onReload();
            toast.success("Data saved successfully!", {
                icon: "✔️",
                theme: "colored",
                position: "top-right",
                autoClose: 3000, 
            });
        } catch (error) {
            console.log(error)
            toast.error("Something went wrong!", {
                icon: "❌",
                theme: "colored",
                position: "top-right",
                autoClose: 3000, 
            });
        } finally {
            setLoading(false);
            setIsProcessing(false);
            toggle();
        }
    }
    return (
        <>
            <button
                type="button" onClick={toggle}
                className="w-full sm:w-auto bg-white border-dashed text-sky-500 btn border-sky-500 hover:text-sky-500 hover:bg-sky-50 hover:border-sky-600 focus:text-sky-600 focus:bg-sky-50 focus:border-sky-600 active:text-sky-600 active:bg-sky-50 active:border-sky-600 dark:bg-zinc-700 dark:ring-sky-400/20 dark:hover:bg-sky-800/20 dark:focus:bg-sky-800/20 dark:active:bg-sky-800/20 text-xs sm:text-sm px-4 sm:px-6 py-2 sm:py-3"
            >
                <i className="align-baseline ltr:pr-1 rtl:pl-1 ri-add-line"></i>Notes
            </button>
             <ToastContainer />


            <Modal show={show} onHide={toggle} modal-center="true"
                className="fixed flex flex-col transition-all duration-300 ease-in-out left-2/4 z-drawer -translate-x-2/4 -translate-y-2/4"
                dialogClassName="w-screen lg:w-[55rem] bg-white shadow rounded-md dark:bg-zink-600">
                <Modal.Header className="flex items-center justify-between p-4 border-b dark:border-zink-500"
                    closeButtonClass="transition-all duration-200 ease-linear text-slate-400 hover:text-red-500">
                    <Modal.Title className="text-16"> Add Notes</Modal.Title>
                </Modal.Header>
                <Modal.Body className="max-h-[calc(theme('height.screen')_-_180px)] p-4 overflow-y-auto">
                    <form onSubmit={handleSubmit(onSubmit)}>
                        <div className="grid grid-cols-1 gap-4 xl:grid-cols-12">
                            <div className="xl:col-span-12">
                                <TextField
                                    label="Notes"
                                    name="note"
                                    register={register}
                                    validation={{ required: "Please Enter Notes Before Save" }}
                                    placeholder="Enter Notes Here"
                                    error={errors.note}
                                    multiline
                                />

                            </div>
                        </div>
                        <div className="flex justify-end gap-2 mt-4">
                            <button type="reset" data-modal-close="addHolidayModal" className="text-red-500 bg-white btn hover:text-red-500 hover:bg-red-100 focus:text-red-500 focus:bg-red-100 active:text-red-500 active:bg-red-100 dark:bg-zink-600 dark:hover:bg-red-500/10 dark:focus:bg-red-500/10 dark:active:bg-red-500/10" onClick={toggle}>Cancel</button>
                            {/* <button 
                                type="submit" 
                                className="text-white btn bg-custom-500 border-custom-500 hover:text-white hover:bg-custom-600 hover:border-custom-600 focus:text-white focus:bg-custom-600 focus:border-custom-600 focus:ring focus:ring-custom-100 active:text-white active:bg-custom-600 active:border-custom-600 active:ring active:ring-custom-100 dark:ring-custom-400/20">
                                Save
                            </button> */}


                            <button
                                type="submit"
                                onClick={handleSubmit}
                                className={`text-white btn bg-custom-500 border-custom-500 hover:text-white hover:bg-custom-600 hover:border-custom-600 focus:text-white focus:bg-custom-600 focus:border-custom-600 focus:ring focus:ring-custom-100 active:text-white active:bg-custom-600 active:border-custom-600 active:ring active:ring-custom-100 dark:ring-custom-400/20 ${isProcessing ? "cursor-not-allowed opacity-70" : ""
                                    }`}
                                disabled={isProcessing}
                            >
                                {isProcessing ? (
                                    <span className="flex items-center">
                                        <Loader2 className="size-4 ltr:mr-2 rtl:ml-2 animate-spin" />
                                        Saving...
                                    </span>
                                ) : (
                                    // <span>{id > 0 ? "Update" : "Initiate"}</span>
                                    <span>Save</span>
                                )}
                            </button>
                        </div>
                    </form>
                </Modal.Body>
            </Modal>

        </>
    );
}

export default AddNotes;
