import { createSlice } from "@reduxjs/toolkit";
import {fetchInitiatedResponse,submittedAuditProduct,repliedAuditProduct,receivedAuditProduct } from './productActions'
import {pendingProduct, submittedProduct, repliedProduct,receivedProduct} from '../../models/product'

const initialState = {
    pendingProduct: {...pendingProduct},
    submittedProduct: {...submittedProduct},
    repliedProduct: {...repliedProduct},
    receivedProduct: {...receivedProduct},
    loading: false,
    error: null,
}


const productSlice = createSlice({
    name: 'auditProduct',
    initialState,
    reducers: {
        setPage(state, action) {
            state.pendingProduct.currentPage = action.payload;
            state.error = null;
        },
        filterFinYear(state, action) {
            const { type, payload } = action.payload;
            if(type === 'P'){
                state.pendingProduct.finYear = payload;
            }else if (type ==='S'){
                state.submittedProduct.finYear = payload;
            }
            else if (type ==='RC'){
                state.receivedProduct.finYear = payload;
            }
            state.error = null;
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchInitiatedResponse.pending , (state) =>{
                state.loading=true
                state.error = null;
            })
            .addCase(fetchInitiatedResponse.fulfilled, (state, action) =>{
                state.loading = false;
                state.pendingProduct = { 
                    ...state.pendingProduct, // Preserve other fields in product
                    data: action.payload.inspectionReportDTOList,
                    totalElements: action.payload.totalElements,
                    totalPages: action.payload.totalPages,
                };
            })
            .addCase(fetchInitiatedResponse.rejected, (state, action)=>{
                state.loading = false;
                state.error = action.payload;
            })
            .addCase(submittedAuditProduct.pending , (state) =>{
                state.loading=true
                state.error = null;
            })
            .addCase(submittedAuditProduct.fulfilled, (state, action) =>{
                state.loading = false;
                state.submittedProduct = { 
                    ...state.submittedProduct, // Preserve other fields in product
                    submittedData: action.payload.inspectionReportDTOList,
                    totalElements: action.payload.totalElements,
                    totalPages: action.payload.totalPages,
                };
            })
            .addCase(submittedAuditProduct.rejected, (state, action)=>{
                state.loading = false;
                state.error = action.payload;
            })
            .addCase(repliedAuditProduct.pending , (state) =>{
                state.loading=true
                state.error = null;
            })
            .addCase(repliedAuditProduct.fulfilled, (state, action) =>{
                state.loading = false;
                state.repliedProduct = { 
                    ...state.repliedProduct, // Preserve other fields in product
                    repliedData: action.payload.inspectionReportDTOList,
                    totalElements: action.payload.totalElements,
                    totalPages: action.payload.totalPages,
                };
            })
            .addCase(repliedAuditProduct.rejected, (state, action)=>{
                state.loading = false;
                state.error = action.payload;
            })
            .addCase(receivedAuditProduct.pending , (state) =>{
                state.loading=true
                state.error = null;
            })
            .addCase(receivedAuditProduct.fulfilled, (state, action) =>{
                state.loading = false;
                state.receivedProduct = { 
                    ...state.receivedProduct, // Preserve other fields in product
                    receivedData: action.payload.inspectionReportDTOList,
                    totalElements: action.payload.totalElements,
                    totalPages: action.payload.totalPages,
                };
            })
            .addCase(receivedAuditProduct.rejected, (state, action)=>{
                state.loading = false;
                state.error = action.payload;
            })
    }
})

export const {
    setPage, filterFinYear
} = productSlice.actions;


export default productSlice.reducer;