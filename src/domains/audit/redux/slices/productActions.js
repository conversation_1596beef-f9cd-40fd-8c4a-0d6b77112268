import { createAsyncThunk } from "@reduxjs/toolkit";

import {getInitiatedAuditList,getSubmittedAuditList,getRepliedAuditList,getReceivedAuditList} from '../../services/auditService';

export const fetchInitiatedResponse = createAsyncThunk(
    'auditProduct/fetchInitiatedResponse',
    async({page,size,finYear}, { rejectWithValue })=>{
        try {
            const response = getInitiatedAuditList({page,size,finYear});
            return response;
        } catch (error) {
            return rejectWithValue(error.message || "An error occurred while fetching data");
        }
    }
)

export const submittedAuditProduct = createAsyncThunk(
    'auditProduct/submittedAuditProduct',
    async({page,size,finYear}, { rejectWithValue })=>{
        try {
            const response = getSubmittedAuditList({page,size,finYear});
            return response;
        } catch (error) {
            return rejectWithValue(error.message || "An error occurred while fetching data");
        }
    }
)

export const repliedAuditProduct = createAsyncThunk(
    'auditProduct/repliedAuditProduct',
    async({page,size}, { rejectWithValue })=>{
        try {
            const response = getRepliedAuditList({page,size});
            return response;
        } catch (error) {
            return rejectWithValue(error.message || "An error occurred while fetching data");
        }
    }
)

export const receivedAuditProduct = createAsyncThunk(
    'auditProduct/receivedAuditProduct',
    async({page,size,seatId,finYear}, { rejectWithValue })=>{
        try {
            const response = getReceivedAuditList({page,size,seatId,finYear});
            return response;
        } catch (error) {
            return rejectWithValue(error.message || "An error occurred while fetching data");
        }
    }
)