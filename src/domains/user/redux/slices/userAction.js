import { createAsyncThunk } from "@reduxjs/toolkit";
import {getUserDetails} from '../../../core/services/generalService'

export const fetchUserDetails = createAsyncThunk(
    'user/fetchUserDetails',
    async({allotId},{rejectWithValue})=>{
        try {
            const response = getUserDetails({allotId});
            return response;
        } catch (error) {
            return rejectWithValue(error.message || "An error occurred while fetching data");
        }
    }
)