import { createSlice } from "@reduxjs/toolkit";
import { userDetails } from '../../../core/models/user'
import { fetchUserDetails } from './userAction'

const initialState = {
    userDetails: { ...userDetails },
    userLoading: false,
    usererror: null,
}

const userSlice = createSlice({
    name: 'user',
    initialState,
    reducers: {
        setUserTheme: (state, action) => {
            state.userDetails.userTheme = action.payload;
        },
        clearUserDetails: (state) => {
            state.userDetails = { ...userDetails };
        }
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchUserDetails.pending, (state) => {
                state.userLoading = true;
                state.usererror = null;
            })
            .addCase(fetchUserDetails.fulfilled, (state, action) => {
                state.userLoading = false;
                state.userDetails = action.payload;
            })
            .addCase(fetchUserDetails.rejected, (state, action) => {
                state.userLoading = false;
                state.usererror = action.payload;
            });
    },
})

export const { setUserTheme, clearUserDetails } = userSlice.actions;

export default userSlice.reducer;