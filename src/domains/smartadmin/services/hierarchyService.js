/**
 * Hierarchy Service
 * Centralizes all API calls related to the hierarchy management system
 */

import { apiClient } from '../../../shared/api/axiosInstance';
import {
  ASSIGN_ADMIN,
  ASSIGN_USER,
  CREATE_NODE,
  DELETE_NODE,
  GET_CHILDREN,
  GET_HIERARCHY_DATA,
  GET_USERS,
  MOVE_NODE,
  UNASSIGN_ADMIN,
  UNASSIGN_USER,
  UPDATE_NODE
} from '../../../shared/api/url';
// Import hierarchy endpoints if needed in the future
// import { HIERARCHY_ENDPOINTS } from '../../../shared/redux/api/hierarchyUrls';

/**
 * Fetch root hierarchy nodes
 * @returns {Promise} Promise object representing the root hierarchy nodes
 */
export const fetchRootHierarchy = () => {
  return apiClient.get(GET_CHILDREN, { parentId: null });
};

/**
 * Fetch children nodes for a specific parent node
 * @param {string|number} parentId - The ID of the parent node
 * @returns {Promise} Promise object representing the children nodes
 */
export const fetchChildrenByParentId = (parentId) => {
  return apiClient.get(GET_CHILDREN, { parentId });
};

/**
 * Create a new hierarchy node
 * @param {Object} nodeData - The data for the new node
 * @param {string} nodeData.name - The name of the new node
 * @param {string} nodeData.code - The code of the new node
 * @param {string} nodeData.category - The category of the new node (O, D, R, T, S, G)
 * @param {string} nodeData.parentId - The ID of the parent node
 * @param {string} [nodeData.activeStatus='Y'] - The active status of the new node
 * @returns {Promise} Promise object representing the created node or a success message
 * @description The API may return a success message like "saved successfully" instead of the created node object
 */
export const createHierarchyNode = (nodeData) => {
  return apiClient.create(CREATE_NODE, nodeData);
};

/**
 * Update an existing hierarchy node
 * @param {Object} nodeData - The updated data for the node
 * @param {string|number} nodeData.id - The ID of the node to update
 * @returns {Promise} Promise object representing the updated node
 */
export const updateHierarchyNode = (nodeData) => {
  return apiClient.put(UPDATE_NODE, nodeData);
};

/**
 * Move a node to a new parent
 * @param {string|number} nodeId - The ID of the node to move
 * @param {string|number} newParentId - The ID of the new parent node
 * @returns {Promise} Promise object representing the result of the move operation
 */
export const moveHierarchyNode = (nodeId, newParentId) => {
  const moveData = {
    id: newParentId,
    hierarchySetupDTO:{
        hierarchyId:nodeId
    }
  };
  return apiClient.post(MOVE_NODE, moveData);
};

/**
 * Search for users
 * @param {string} searchQuery - The search term to filter users
 * @param {number} [page=0] - The page number for pagination
 * @param {number} [size=20] - The page size for pagination
 * @returns {Promise} Promise object representing the search results
 */
export const searchUsers = (searchTerm, page = 0, size = 20) => {
  return apiClient.get(
    GET_USERS,
 { searchQuery: searchTerm || null, page, size }
  );
};

/**
 * Search for admin users
 * @param {string} searchQuery - The search term to filter admin users
 * @param {number} [page=0] - The page number for pagination
 * @param {number} [size=20] - The page size for pagination
 * @returns {Promise} Promise object representing the admin users list
 */
export const searchAdminUsers = (searchTerm, page = 0, size = 20) => {
  return apiClient.get(
    '/hierarchyAdmin',
    { searchQuery: searchTerm || null, page, size }
  );
};

/**
 * Assign a user to a node
 * @param {string|number} hierarchyId - The ID of the node
 * @param {string|number} userId - The ID of the user to assign
 * @returns {Promise} Promise object representing the result of the assignment
 */
export const assignUserToNode = (hierarchyId, userId) => {
  const payload = { hierarchyId, userId };
  return apiClient.create(ASSIGN_USER, payload);
};

/**
 * Unassign a user from a node
 * @param {string|number} hierarchyId - The ID of the node
 * @returns {Promise} Promise object representing the result of the unassignment
 */
export const unassignUserFromNode = (hierarchyId) => {
  const payload = { hierarchyId, userId: null };
  return apiClient.create(UNASSIGN_USER, payload);
};

/**
 * Delete a hierarchy node
 * @param {string|number} nodeId - The ID of the node to delete
 * @returns {Promise} Promise object representing the result of the deletion
 */
export const deleteHierarchyNode = (nodeId) => {
  return apiClient.delete(`${DELETE_NODE}/${nodeId}`);
};

/**
 * Search for posts
 * @param {string} searchTerm - The search term to filter posts
 * @param {number} [page=0] - The page number for pagination
 * @param {number} [size=20] - The page size for pagination
 * @returns {Promise} Promise object representing the search results
 */
export const searchPosts = (searchTerm, page = 0, size = 20) => {
  // Since there's no actual API endpoint for posts yet, we'll use mock data
  const searchQuery = searchTerm ? searchTerm : null;

  // Mock data for posts
  const mockPosts = [
    { id: 1, name: 'Senior Accounts Officer' },
    { id: 2, name: 'Director' },
    { id: 3, name: 'Supervisor' },
    { id: 4, name: 'Assistant Supervisor' },
    { id: 5, name: 'Additional Secretary Department' },
    { id: 6, name: 'Additional Secretary EC' },
    { id: 7, name: 'Additional Secretary SIU' },
    { id: 8, name: 'Administrator' },
    { id: 9, name: 'ASPIRe Support Team' },
    { id: 10, name: 'Assistant' }
  ];

  // Filter posts based on search term
  const filteredPosts = searchQuery
    ? mockPosts.filter(post =>
        post.name.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : mockPosts;

  // Simulate pagination
  const startIndex = page * size;
  const endIndex = startIndex + size;
  const paginatedPosts = filteredPosts.slice(startIndex, endIndex);

  // Return a promise to match the API pattern
  return Promise.resolve(paginatedPosts);

  // When an actual API endpoint is available, use this pattern:
  // return apiClient.get(
  //   GET_POSTS, // This would be a new URL constant
  //   { params: {searchQuery, page, size } }
  // );
};

/**
 * Assign an admin to a node
 * @param {string|number} hierarchyId - The ID of the node
 * @param {string|number} userId - The ID of the admin user to assign
 * @returns {Promise} Promise object representing the result of the assignment
 */
export const assignAdminToNode = (hierarchyId, userId) => {
  const payload = { hierarchyId, userId };
  return apiClient.create(ASSIGN_ADMIN, payload);
};

/**
 * Unassign an admin from a node
 * @param {string|number} hierarchyId - The ID of the node
 * @returns {Promise} Promise object representing the result of the unassignment
 */
export const unassignAdminFromNode = (hierarchyId) => {
  const payload = { hierarchyId, userId: null };
  return apiClient.create(UNASSIGN_ADMIN, payload);
};

/**
 * Fetch hierarchy data by ID and type
 * @param {string|number} hierarchyId - The ID of the hierarchy node
 * @param {string} type - The type parameter (e.g., 'N')
 * @returns {Promise} Promise object representing the hierarchy data
 * @description The response includes posting details, parent hierarchy name, and office types
 */
export const fetchHierarchyData = (hierarchyId, type = 'N') => {
  return apiClient.get(GET_HIERARCHY_DATA, {
    id: hierarchyId,  // Backend expects 'id' parameter, not 'hierarchyId'
    type
  });
};

export default {
  fetchRootHierarchy,
  fetchChildrenByParentId,
  createHierarchyNode,
  updateHierarchyNode,
  moveHierarchyNode,
  searchUsers,
  searchAdminUsers,
  assignUserToNode,
  unassignUserFromNode,
  assignAdminToNode,
  unassignAdminFromNode,
  deleteHierarchyNode,
  searchPosts,
  fetchHierarchyData
};
