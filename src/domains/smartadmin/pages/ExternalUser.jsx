import { useState } from 'react';
import UserTable from './UserTableComponent';

const ExternalUser = () => {
    const [showModal, setShowModal] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [entriesPerPage, setEntriesPerPage] = useState(15);
    const [showViewModal, setShowViewModal] = useState(false);
    const [viewUser, setViewUser] = useState(null);
    const [editUserId, setEditUserId] = useState(null);

    const [formData, setFormData] = useState({
        title: '',
        firstName: '',
        lastName: '',
        pan: '',
        email: '',
        dob: '',
        mobile: '',
        post: '',
        gender: '',
        attachment: null
    });

    const titles = ['Mr.', 'Mrs.', 'Ms.', 'Miss', 'Dr.', 'Prof.', 'Shri', 'Smt.'];

    const [users, setUsers] = useState([
        { id: 1, name: "Mr. <PERSON><PERSON>", pan: "**********", email: "<EMAIL>", mobile: "*********", post: "Senior Accounts Officer", office: "AG (AE) & Technology Department" },
        { id: 2, name: "Shri Ashwani Kumar, IAS", pan: "**********", email: "<EMAIL>", mobile: "*********", post: "Director", office: "Information & Technology Department" },
        { id: 3, name: "Mr. Brig Polash Choudhury, SM (Retd)", pan: "**********", email: "<EMAIL>", mobile: "**********", post: "Director", office: "General Administration Department" },
        { id: 4, name: "Mr. Dipak Chandra Baidya", pan: "ABPB9620F", email: "<EMAIL>", mobile: "**********", post: "Supervisor", office: "AG (AE)" },
        { id: 5, name: "Mr. Krishna Basi Das", pan: "ACBDP048A", email: "<EMAIL>", mobile: "*********", post: "Senior Accounts Officer", office: "AG (AE)" },
        { id: 6, name: "Mr. Ratna Biswas", pan: "ABXPB972A", email: "<EMAIL>", mobile: "*********", post: "Assistant Supervisor", office: "AG (AE)" },
        { id: 7, name: "Mr. Soumen Sarkar", pan: "**********", email: "<EMAIL>", mobile: "**********", post: "Supervisor", office: "AG (AE)" }
    ]);

    const [searchQuery, setSearchQuery] = useState('');
    const [selectedDepartment, setSelectedDepartment] = useState('');

    const posts = [
        'Senior Accounts Officer',
        'Director',
        'Supervisor',
        'Assistant Supervisor'
    ];

    const departments = [...new Set(users.map(user => user.office))];

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, [name]: value }));
    };

    const handleFileChange = (e) => {
        setFormData(prev => ({ ...prev, attachment: e.target.files[0] }));
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        if (!/^\d{10}$/.test(formData.mobile)) {
            alert('Invalid mobile number. Please enter a 10-digit number.');
            return;
        }

        const newId = users.length > 0 ? Math.max(...users.map(user => user.id)) + 1 : 1;

        const newUser = {
            id: newId,
            name: `${formData.title} ${formData.firstName} ${formData.lastName}`,
            pan: formData.pan,
            email: formData.email,
            dob: formData.dob,
            mobile: formData.mobile,
            post: formData.post,
            gender: formData.gender,
            attachment: formData.attachment,
            office: "AG (AE)"
        };

        setUsers([...users, newUser]);
        setFormData({
            title: '',
            firstName: '',
            lastName: '',
            pan: '',
            email: '',
            dob: '',
            mobile: '',
            post: '',
            gender: '',
            attachment: null
        });
        setShowModal(false);
    };

    const filteredUsers = users.filter(user => {
        const matchesSearch = (
            user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            user.pan.toLowerCase().includes(searchQuery.toLowerCase()) ||
            user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
            user.mobile.includes(searchQuery) ||
            user.post.toLowerCase().includes(searchQuery.toLowerCase()) ||
            user.office.toLowerCase().includes(searchQuery.toLowerCase())
        );
        const matchesDepartment = selectedDepartment === '' || user.office === selectedDepartment;
        return matchesSearch && matchesDepartment;
    });


    const indexOfLastUser = currentPage * entriesPerPage;
    const indexOfFirstUser = indexOfLastUser - entriesPerPage;
    const currentUsers = filteredUsers.slice(indexOfFirstUser, indexOfLastUser);
    const totalPages = Math.ceil(filteredUsers.length / entriesPerPage);

    const handlePageChange = (pageNumber) => {
        setCurrentPage(pageNumber);
    };

    const handleEntriesChange = (e) => {
        setEntriesPerPage(Number(e.target.value));
        setCurrentPage(1);
    };

    const headers = [
        { key: 'index', label: '#' },
        { key: 'name', label: 'User Name' },
        { key: 'pan', label: 'PAN' },
        { key: 'email', label: 'Email' },
        { key: 'mobile', label: 'Mobile No' },
        { key: 'post', label: 'Post' },
        { key: 'office', label: 'Office/Directorate' },
        { key: 'actions', label: '' },
        
    ];

    const handleEdit = (user) => {
        setEditUserId(user.id);
        setFormData({ name: user.name, type: user.type, status: user.status });
        setShowModal(true);
    };


    const handleView = (user) => {
        setViewUser(user);
        setShowViewModal(true);
    };


    return (
        <div className="bg-gray-100 font-sans min-h-screen p-4">
            <div className="container mx-auto">
                <div className="flex flex-wrap justify-between items-center mb-4">
                    <h2 className="text-xl font-bold text-gray-700">External User Setup</h2>
                    <button
                        className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
                        onClick={() => setShowModal(true)}
                    >
                        Add New
                    </button>
                </div>

                {showModal && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                        <div className="bg-white  rounded-lg w-full max-w-2xl ">
                        <div className='bg-green-600 p-2 flex justify-between text-white'> 
                        <h3 className="text-lg font-bold mb-4  m-2 text-white ">Add New User</h3>
                        <button type='button' onClick={() => setShowModal(false)}><span className='font-bold text-2xl m-2'>×</span></button>
                        </div>
                            <div className='p-6'>
                           
                            <form onSubmit={handleSubmit}>
                                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-4">
                                    <select
                                        name="title"
                                        value={formData.title}
                                        onChange={handleInputChange}
                                        className="w-full p-2 border rounded"
                                        required
                                    >
                                        <option value=""></option>
                                        {titles.map((t, i) => <option key={i} value={t}>{t}</option>)}
                                    </select>
                                    <input
                                        type="text"
                                        name="firstName"
                                        placeholder="First Name"
                                        value={formData.firstName}
                                        onChange={handleInputChange}
                                        className="w-full p-2 border rounded"
                                        required
                                    />
                                    <input
                                        type="text"
                                        name="lastName"
                                        placeholder="Last Name"
                                        value={formData.lastName}
                                        onChange={handleInputChange}
                                        className="w-full p-2 border rounded"
                                        required
                                    />
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label className="block mb-2">PAN <span className="text-red-500">*</span></label>
                                        <input
                                            type="text"
                                            name="pan"
                                            value={formData.pan}
                                            onChange={handleInputChange}
                                            className="w-full p-2 border rounded uppercase tracking-wider"
                                        />
                                    </div>
                                    <div>
                                        <label className="block mb-2">Email <span className="text-red-500">*</span></label>
                                        <input
                                            type="email"
                                            name="email"
                                            className="w-full p-2 border rounded"
                                            value={formData.email}
                                            onChange={handleInputChange}
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block mb-2">Date of Birth <span className="text-red-500">*</span></label>
                                        <input
                                            type="date"
                                            name="dob"
                                            className="w-full p-2 border rounded"
                                            value={formData.dob}
                                            onChange={handleInputChange}
                                            max={new Date().toISOString().split('T')[0]}
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block mb-2">Mobile <span className="text-red-500">*</span></label>
                                        <input
                                            type="tel"
                                            name="mobile"
                                            className="w-full p-2 border rounded"
                                            value={formData.mobile}
                                            onChange={handleInputChange}
                                            pattern="[0-9]{10}"
                                            title="10 digit mobile number"
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block mb-2">Post <span className="text-red-500">*</span></label>
                                        <select
                                            name="post"
                                            className="w-full p-2 border rounded"
                                            value={formData.post}
                                            onChange={handleInputChange}
                                            required
                                        >
                                            <option value="">Select Position</option>
                                            {posts.map((post, index) => (
                                                <option key={index} value={post}>{post}</option>
                                            ))}
                                        </select>
                                    </div>
                                    <div>
                                        <label className="block mb-2">Gender <span className="text-red-500">*</span></label>
                                        <div className="flex gap-4">
                                            <label className="flex items-center">
                                                <input
                                                    type="radio"
                                                    name="gender"
                                                    value="male"
                                                    checked={formData.gender === 'male'}
                                                    onChange={handleInputChange}
                                                    required
                                                    className="mr-2"
                                                />
                                                Male
                                            </label>
                                            <label className="flex items-center">
                                                <input
                                                    type="radio"
                                                    name="gender"
                                                    value="female"
                                                    checked={formData.gender === 'female'}
                                                    onChange={handleInputChange}
                                                    className="mr-2"
                                                />
                                                Female
                                            </label>
                                            <label className="flex items-center">
                                                <input
                                                    type="radio"
                                                    name="gender"
                                                    value="transgender"
                                                    checked={formData.gender === 'transgender'}
                                                    onChange={handleInputChange}
                                                    className="mr-2"
                                                />
                                                Transgender
                                            </label>
                                        </div>
                                    </div>
                                    <div>
                                        <label className="block mb-2">Attachment <span className="text-red-500">*</span></label>
                                        <input
                                            type="file"
                                            className="w-full p-2 border rounded"
                                            onChange={handleFileChange}
                                            accept=".pdf,.doc,.docx,.jpg,.png"
                                        />
                                        <p className="text-sm text-gray-500 mt-1">Accepted formats: PDF, DOC, JPG, PNG</p>
                                    </div>
                                </div>
                                <div className="mt-6 flex justify-end gap-4">
                                    {/* <button
                                        type="button"
                                        className="px-4 py-2 border rounded"
                                        onClick={() => setShowModal(false)}
                                    >
                                        Cancel
                                    </button> */}
                                    <button
                                        type="submit"
                                        className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                                    >
                                        Save
                                    </button>
                                </div>
                            </form>
                        </div>
                        </div>
                    </div>
                )}
                {showViewModal && viewUser && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                        <div className="bg-white  rounded-lg w-full max-w-2xl">
                        <div className='bg-green-600 p-2 flex justify-between text-white'> 
                        <h3 className="text-lg font-bold mb-4  m-2 text-white ">View User Details</h3>
                        <button type='button' onClick={() => setShowViewModal(false)}><span className='font-bold text-2xl m-2'>×</span></button>
                        </div>
                            <div className='p-6'>
                          
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label className="block mb-2 font-semibold">Name</label>
                                    <p>{viewUser.name}</p>
                                </div>
                                <div>
                                    <label className="block mb-2 font-semibold">PAN</label>
                                    <p>{viewUser.pan}</p>
                                </div>
                                <div>
                                    <label className="block mb-2 font-semibold">Email</label>
                                    <p>{viewUser.email}</p>
                                </div>
                                <div>
                                    <label className="block mb-2 font-semibold">Date of Birth</label>
                                    <p>{viewUser.dob || 'N/A'}</p>
                                </div>
                                <div>
                                    <label className="block mb-2 font-semibold">Mobile</label>
                                    <p>{viewUser.mobile}</p>
                                </div>
                                <div>
                                    <label className="block mb-2 font-semibold">Post</label>
                                    <p>{viewUser.post}</p>
                                </div>
                                <div>
                                    <label className="block mb-2 font-semibold">Gender</label>
                                    <p>{viewUser.gender || 'N/A'}</p>
                                </div>
                                <div>
                                    <label className="block mb-2 font-semibold">Office</label>
                                    <p>{viewUser.office}</p>
                                </div>
                                <div>
                                    <label className="block mb-2 font-semibold">Attachment</label>
                                    <p>{viewUser.attachment ? viewUser.attachment.name : 'N/A'}</p>
                                </div>
                            </div>
                            <div className="mt-6 flex justify-end">
                                {/* <button
                                    type="button"
                                    className="px-4 py-2 border rounded"
                                    onClick={() => setShowViewModal(false)}
                                >
                                    Close
                                </button> */}
                            </div>
                            </div>
                        </div>
                    </div>
                )}

                <div className="flex flex-col md:flex-row gap-4 mb-4">
                    <input
                        type="text"
                        placeholder="Search..."
                        className="w-full md:w-1/3 p-2 border rounded"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                    />
                    <select
                        className="w-full md:w-1/3 p-2 border rounded"
                        value={selectedDepartment}
                        onChange={(e) => setSelectedDepartment(e.target.value)}
                    >
                        <option value="">All Departments</option>
                        {departments.map((dept, index) => (
                            <option key={index} value={dept}>{dept}</option>
                        ))}
                    </select>
                   
                </div>

                <div className="overflow-x-auto">
                    <UserTable
                        headers={headers}
                        data={currentUsers.map((user, index) => ({
                            ...user,
                            index: indexOfFirstUser + index + 1,
                            actions: (
                                <div className="flex space-x-2">
                                  <button
                                    className="text-blue-600 hover:text-blue-800"
                                    onClick={() => handleView(user)}
                                    title="Edit"
                                  >
                                    <i className="fas fa-eye"></i>
                                  </button>
                                 
                                </div>
                              ),
                            }))}
                    />
                </div>
                <div className="flex justify-between items-center mt-4">
                    <div>
                   <label>Show</label>
                    <select
                        className="w-full md:w-24 p-1.5 border rounded text-sm"
                        value={entriesPerPage}
                        onChange={handleEntriesChange}
                    >
                        <option value={15}>15</option>
                        <option value={20}>20</option>
                        <option value={30}>30</option>
                    </select>
                    </div>
                    <span>Showing {indexOfFirstUser + 1} to {Math.min(indexOfLastUser, filteredUsers.length)} of {filteredUsers.length} entries</span>
                    <div className="flex space-x-2">
                        <button
                            className="px-3 py-1 border rounded disabled:opacity-50"
                            onClick={() => handlePageChange(currentPage - 1)}
                            disabled={currentPage === 1}
                        >
                            Previous
                        </button>
                        {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                            <button
                                key={page}
                                className={`px-3 py-1 border rounded ${currentPage === page ? 'bg-green-600 text-white' : ''}`}
                                onClick={() => handlePageChange(page)}
                            >
                                {page}
                            </button>
                        ))}
                        <button
                            className="px-3 py-1 border rounded disabled:opacity-50"
                            onClick={() => handlePageChange(currentPage + 1)}
                            disabled={currentPage === totalPages}
                        >
                            Next
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ExternalUser;