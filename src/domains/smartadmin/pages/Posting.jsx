import  { useState } from 'react';
import UserTable from './UserTableComponent';

const Posting = () => {
    const [showModal, setShowModal] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [entriesPerPage, setEntriesPerPage] = useState(15);
    const [editUserId, setEditUserId] = useState(null);

    const [formData, setFormData] = useState({
        name: '',
        post: '',
        type: '',
        status: ''
    });


    const [users, setUsers] = useState([
        { id: 1, name: "Account Assistant", type: "Both", status: "Active" },
        { id: 2, name: "Accountant", type: "Both", status: "Active" },
        { id: 3, name: "Accountant General", type: "Both", status: "Active" },
        { id: 4, name: "Accounts Officer", type: "Both", status: "Active" },
        { id: 5, name: "Additional Chief Engineer", type: "Both", status: "Active" },
        { id: 6, name: "Additional Chief Secretary", type: "Both", status: "Active" },
        { id: 7, name: "Additional Chief Secretary Department", type: "Both", status: "Active" },
        { id: 8, name: "Additional Director Department", type: "Both", status: "Active" },
        { id: 9, name: "Additional Secretary", type: "Both", status: "Active" },
        { id: 10, name: "Additional Secretary Department", type: "Both", status: "Active" },
        { id: 11, name: "Additional Secretary EC", type: "Both", status: "Active" },
        { id: 12, name: "Additional Secretary SIU", type: "Both", status: "Active" },
        { id: 13, name: "Administrator", type: "Both", status: "Active" },
        { id: 14, name: "ASPIRe Support Team", type: "Both", status: "Active" },
        { id: 15, name: "Assistant", type: "Both", status: "Active" },
    ]);

    const [searchQuery, setSearchQuery] = useState('');


    const posts = [
        'Post',
        'Designation',
        'Both'
    ];



    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, [name]: value }));
    };


    const handleSubmit = (e) => {
        e.preventDefault();
        if (editUserId) {
            setUsers(
                users.map((user) =>
                    user.id === editUserId
                        ? { ...user, name: formData.name, type: formData.type, status: 'Active' }
                        : user
                )
            );
            setEditUserId(null);
        } else {
            const newId = users.length > 0 ? Math.max(...users.map((user) => user.id)) + 1 : 1;
            const newUser = {
                id: newId,
                name: formData.name,
                type: formData.type,
                status: 'Active',
            };
            setUsers([...users, newUser]);
        }
        setFormData({ name: '', type: '', status: '' });
        setShowModal(false);
    };

    const handleEdit = (user) => {
        setEditUserId(user.id);
        setFormData({ name: user.name, type: user.type, status: user.status });
        setShowModal(true);
    };

    const handleDelete = (userId) => {
        if (window.confirm('Are you sure you want to delete this user?')) {
            setUsers(users.filter((user) => user.id !== userId));
        }
    };

    const filteredUsers = users.filter(user => {
        const matchesSearch = (
            user.name.toLowerCase().includes(searchQuery.toLowerCase())

        );

        return matchesSearch;
    });


    const indexOfLastUser = currentPage * entriesPerPage;
    const indexOfFirstUser = indexOfLastUser - entriesPerPage;
    const currentUsers = filteredUsers.slice(indexOfFirstUser, indexOfLastUser);
    const totalPages = Math.ceil(filteredUsers.length / entriesPerPage);

    const handlePageChange = (pageNumber) => {
        setCurrentPage(pageNumber);
    };

    const handleEntriesChange = (e) => {
        setEntriesPerPage(Number(e.target.value));
        setCurrentPage(1);
    };

    const headers = [
        { key: 'index', label: '#' },
        { key: 'name', label: 'Name' },
        { key: 'type', label: 'Type' },
        { key: 'status', label: 'Status' },
        { key: 'actions', label: '' },
    ];




    return (
        <div className="bg-gray-100 font-sans min-h-screen p-4">
            <div className="container mx-auto">
                <div className="flex flex-wrap justify-between items-center mb-4">
                    <h2 className="text-xl font-bold text-gray-700">Post / Designation Setup </h2>
                    <button
                        className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
                        onClick={() => {
                            setEditUserId(null);
                            setFormData({ name: '', type: '', status: '' });
                            setShowModal(true);
                        }}
                    >
                        Add New
                    </button>
                </div>

                {showModal && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                        <div className="bg-white rounded-lg w-full max-w-2xl">
                        <div className='bg-green-600 p-2 flex justify-between text-white'> 
                        <h3 className="text-lg font-bold mb-4  m-2 text-white ">Post Setup</h3>
                        <button type='button' onClick={() => setShowModal(false)}><span className='font-bold text-2xl m-2'>×</span></button>
                        </div>
                            <div className='p-6'>
                       
                            <form onSubmit={handleSubmit}>
                                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-4">
                                    <div>
                                        <label className="block mb-2">Post Name <span className="text-red-500">*</span></label>
                                        <input
                                            name="name"
                                            type='text'
                                            className="w-full p-2 border rounded"
                                            value={formData.name}
                                            onChange={handleInputChange}
                                            required
                                        />

                                    </div>

                                    <div>
                                        <label className="block mb-2">Type <span className="text-red-500">*</span></label>
                                        <select
                                            name="type"
                                            className="w-full p-2 border rounded"
                                            value={formData.type}
                                            onChange={handleInputChange}
                                            required
                                        >
                                            <option value="">Select Position</option>
                                            {posts.map((post, index) => (
                                                <option key={index} value={post}>{post}</option>
                                            ))}
                                        </select>
                                    </div>
                                </div>
                                <div className="mt-6 flex justify-end gap-4">
                                    {/* <button
                                        type="button"
                                        className="px-4 py-2 border rounded"
                                        onClick={() => setShowModal(false)}
                                    >
                                        Cancel
                                    </button> */}
                                    <button
                                        type="submit"
                                        className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                                    >
                                        {editUserId ? 'Update' : 'Save'}
                                    </button>
                                </div>
                            </form>
                        </div>
                        </div>
                    </div>
                )}

                <div className="flex flex-col md:flex-row gap-4 mb-4">
                    <input
                        type="text"
                        placeholder="Search..."
                        className="w-full md:w-1/3 p-2 border rounded"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                    />


                </div>

                <div className="overflow-x-auto">
                    <UserTable
                        headers={headers}
                        data={currentUsers.map((user, index) => ({
                            ...user,
                            index: indexOfFirstUser + index + 1,
                            actions: (
                                <div className="flex space-x-2">
                                  <button
                                    className="text-blue-600 hover:text-blue-800"
                                    onClick={() => handleEdit(user)}
                                    title="Edit"
                                  >
                                    <i className="fas fa-edit"></i>
                                  </button>
                                  <button
                                    className="text-red-600 hover:text-red-800"
                                    onClick={() => handleDelete(user.id)}
                                    title="Delete"
                                  >
                                    <i className="fas fa-trash-alt"></i>
                                  </button>
                                </div>
                              ),
                            }))}
                    />
                </div>
                <div className="flex justify-between items-center mt-4">
                    <div>
                        <label>Show</label>
                        <select
                            className="w-full md:w-24 p-1.5 border rounded text-sm"
                            value={entriesPerPage}
                            onChange={handleEntriesChange}
                        >
                            <option value={15}>15</option>
                            <option value={20}>20</option>
                            <option value={30}>30</option>
                        </select>
                    </div>
                    <span>Showing {indexOfFirstUser + 1} to {Math.min(indexOfLastUser, filteredUsers.length)} of {filteredUsers.length} entries</span>
                    <div className="flex space-x-2">
                        <button
                            className="px-3 py-1 border rounded disabled:opacity-50"
                            onClick={() => handlePageChange(currentPage - 1)}
                            disabled={currentPage === 1}
                        >
                            Previous
                        </button>
                        {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                            <button
                                key={page}
                                className={`px-3 py-1 border rounded ${currentPage === page ? 'bg-red-600 text-white' : ''}`}
                                onClick={() => handlePageChange(page)}
                            >
                                {page}
                            </button>
                        ))}
                        <button
                            className="px-3 py-1 border rounded disabled:opacity-50"
                            onClick={() => handlePageChange(currentPage + 1)}
                            disabled={currentPage === totalPages}
                        >
                            Next
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Posting
