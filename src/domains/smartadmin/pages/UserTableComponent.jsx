
const UserTableComponent = ({ headers, data }) => (
  <table className="min-w-full bg-white border border-gray-300">
    <thead>
      <tr className="bg-gray-200 text-gray-600 uppercase text-sm leading-normal">
        {headers.map((header, index) => (
          <th key={index} className="py-3 px-6 text-left">
            {header.label}
          </th>
        ))}
      </tr>
    </thead>
    <tbody className="text-gray-600 text-sm">
      {data.length > 0 ? data.map((row, rowIndex) => (
        <tr
          key={row.id}
          className={`border-b border-gray-200 ${rowIndex % 2 === 0 ? 'bg-gray-50' : 'bg-white'
            } hover:bg-gray-100`}
        >
          {headers.map((header) => (
            <td
              key={header.key}
              className={`py-2 px-6 text-left
               
                ${header.key === 'status' && row.status === 'Active'
                  ? 'text-green-700'
                  : ''
                }${header.key === 'status' && row.status === 'Deactive'
                  ? 'text-red-600'
                  : ''
                }`}

            >
              {row[header.key]}
            </td>
          ))}
        </tr>
      ))
    : 
    <tr>
    <td 
      colSpan={12} 
      className="border-b border-gray-200 hover:bg-gray-100 text-center p-2 font-bold"
    >
      No data available in table
    </td>
  </tr>
    }
    </tbody>
  </table>
);

export default UserTableComponent;