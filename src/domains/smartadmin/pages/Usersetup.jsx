import { useState } from "react";
import UserTable from "./UserTableComponent";

/**
 * UserSetup component for managing user data
 * 
 * @returns {JSX.Element} - Rendered UserSetup component
 */
const UserSetup = () => {
  const [showModal, setShowModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [entriesPerPage, setEntriesPerPage] = useState(15);
  const [currentPage, setCurrentPage] = useState(1);
  const [editUserId, setEditUserId] = useState(null);

  const [formData, setFormData] = useState({
    name: "",
    email: "",
    mobile: "",
    dob: "",
    gender: "",
    userCode: "",
    seatsAlloted: ""
  });

  const [users, setUsers] = useState(
    Array.from({ length: 50 }, (_, i) => ({
      id: i + 1,
      name: `User ${i + 1}`,
      email: `user${i+1}@example.com`,
      mobile: `99477${String(i).padStart(5, '0')}`,
      userCode: `DAR/SWD/00${i+1}-Darrang (DAR/SWD/00${i+1}-Darrang)`,
      seatsAlloted: i % 3 === 0 ? "2" : "1"
    }))
  );

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (editUserId) {
      setUsers(
        users.map((user) =>
          user.id === editUserId
            ? {
                ...user,
                name: formData.name,
                email: formData.email,
                mobile: formData.mobile,
                dob: formData.dob,
                gender: formData.gender,
                userCode: formData.userCode,
                seatsAlloted: formData.seatsAlloted
              }
            : user
        )
      );
      setEditUserId(null);
    } else {
      const newId = users.length > 0 ? Math.max(...users.map((user) => user.id)) + 1 : 1;
      const newUser = {
        id: newId,
        name: formData.name,
        email: formData.email,
        mobile: formData.mobile,
        dob: formData.dob,
        gender: formData.gender,
        userCode: formData.userCode,
        seatsAlloted: formData.seatsAlloted
      };
      setUsers([...users, newUser]);
    }
    
    setFormData({ name: "", email: "", mobile: "", dob: "", gender: "", userCode: "", seatsAlloted: "" });
    setShowModal(false);
  };

  const handleEdit = (user) => {
    setEditUserId(user.id);
    setFormData({
      name: user.name,
      email: user.email,
      mobile: user.mobile,
      dob: user.dob || "",
      gender: user.gender || "",
      userCode: user.userCode,
      seatsAlloted: user.seatsAlloted
    });
    setShowModal(true);
  };

  const handleDelete = (userId) => {
    if (window.confirm('Are you sure you want to delete this user?')) {
      setUsers(users.filter((user) => user.id !== userId));
    }
  };

  const filteredUsers = users.filter(user => {
    return (
      user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.mobile.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.userCode.toLowerCase().includes(searchQuery.toLowerCase())
    );
  });

  const indexOfLastUser = currentPage * entriesPerPage;
  const indexOfFirstUser = indexOfLastUser - entriesPerPage;
  const currentUsers = filteredUsers.slice(indexOfFirstUser, indexOfLastUser);
  const totalPages = Math.ceil(filteredUsers.length / entriesPerPage);

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  const handleEntriesChange = (e) => {
    setEntriesPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  const headers = [
    { key: 'index', label: '#' },
    { key: 'name', label: 'Employee Name' },
    { key: 'userCode', label: 'User Code' },
    { key: 'seatsAlloted', label: 'Seats Alloted' },
    { key: 'email', label: 'Email' },
    { key: 'mobile', label: 'Mobile No' },
    { key: 'actions', label: 'Actions' },
  ];

  return (
    <div className="bg-gray-100 font-sans min-h-screen p-4">
      <div className="container mx-auto">
        <div className="flex flex-wrap justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-700">User Setup</h2>
          <button
            className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
            onClick={() => {
              setEditUserId(null);
              setFormData({ name: "", email: "", mobile: "", dob: "", gender: "", userCode: "", seatsAlloted: "" });
              setShowModal(true);
            }}
          >
            Add New
          </button>
        </div>

        {showModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
            <div className="bg-white rounded-lg w-full max-w-2xl">
              <div className='bg-green-600 p-2 flex justify-between text-white'> 
                <h3 className="text-lg font-bold mb-4 m-2 text-white">User Setup</h3>
                <button type='button' onClick={() => setShowModal(false)}>
                  <span className='font-bold text-2xl m-2'>×</span>
                </button>
              </div>
              
              <div className='p-6'>
                <form onSubmit={handleSubmit}>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Name <span className="text-red-500">*</span></label>
                      <input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm p-2 border"
                        placeholder="Enter user name"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Email <span className="text-red-500">*</span></label>
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm p-2 border"
                        placeholder="Enter email"
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Mobile <span className="text-red-500">*</span></label>
                      <input
                        type="text"
                        name="mobile"
                        value={formData.mobile}
                        onChange={handleInputChange}
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm p-2 border"
                        placeholder="Enter mobile number"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">User Code <span className="text-red-500">*</span></label>
                      <input
                        type="text"
                        name="userCode"
                        value={formData.userCode}
                        onChange={handleInputChange}
                        required
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm p-2 border"
                        placeholder="Enter user code"
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Seats Alloted</label>
                      <input
                        type="text"
                        name="seatsAlloted"
                        value={formData.seatsAlloted}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm p-2 border"
                        placeholder="Enter seats alloted"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Date of Birth</label>
                      <input
                        type="date"
                        name="dob"
                        value={formData.dob}
                        onChange={handleInputChange}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm p-2 border"
                      />
                    </div>
                  </div>
                  
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700">Gender</label>
                    <select
                      name="gender"
                      value={formData.gender}
                      onChange={handleInputChange}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm p-2 border"
                    >
                      <option value="">Select Gender</option>
                      <option value="Male">Male</option>
                      <option value="Female">Female</option>
                      <option value="Transgender">Transgender</option>
                    </select>
                  </div>
                  
                  <div className="mt-6 flex justify-end gap-4">
                    <button
                      type="submit"
                      className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                    >
                      {editUserId ? 'Update' : 'Save'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        )}

        <div className="flex flex-col md:flex-row gap-4 mb-4">
          <input
            type="text"
            placeholder="Search..."
            className="w-full md:w-1/3 p-2 border rounded"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <div className="overflow-x-auto">
          <UserTable
            headers={headers}
            data={currentUsers.map((user, index) => ({
              ...user,
              index: indexOfFirstUser + index + 1,
              actions: (
                <div className="flex space-x-2">
                  <button
                    className="text-blue-600 hover:text-blue-800"
                    onClick={() => handleEdit(user)}
                    title="Edit"
                  >
                    <i className="fas fa-edit"></i>
                  </button>
                  <button
                    className="text-red-600 hover:text-red-800"
                    onClick={() => handleDelete(user.id)}
                    title="Delete"
                  >
                    <i className="fas fa-trash-alt"></i>
                  </button>
                </div>
              ),
            }))}
          />
        </div>

        <div className="flex justify-between items-center mt-4">
          <div>
            <label>Show</label>
            <select
              className="w-full md:w-24 p-1.5 border rounded text-sm ml-2"
              value={entriesPerPage}
              onChange={handleEntriesChange}
            >
              <option value={15}>15</option>
              <option value={20}>20</option>
              <option value={30}>30</option>
            </select>
          </div>
          <span>Showing {indexOfFirstUser + 1} to {Math.min(indexOfLastUser, filteredUsers.length)} of {filteredUsers.length} entries</span>
          <div className="flex space-x-2">
            <button
              className="px-3 py-1 border rounded disabled:opacity-50"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
            >
              Previous
            </button>
            {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
              <button
                key={page}
                className={`px-3 py-1 border rounded ${currentPage === page ? 'bg-green-600 text-white' : ''}`}
                onClick={() => handlePageChange(page)}
              >
                {page}
              </button>
            ))}
            <button
              className="px-3 py-1 border rounded disabled:opacity-50"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserSetup;
