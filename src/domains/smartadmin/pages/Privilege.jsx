import { useState } from 'react';
import UserTable from './UserTableComponent';

const Privilege = () => {
    const [showModal, setShowModal] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [entriesPerPage, setEntriesPerPage] = useState(15);

    const [currentPageModules, setCurrentPageModules] = useState(1);
    const [entriesPerPageModules, setEntriesPerPageModules] = useState(15);
    const [searchQueryModules, setSearchQueryModules] = useState('');
    const [selectedUser, setSelectedUser] = useState(null); 
  const [selectedModules, setSelectedModules] = useState([]);

    



    const [users, setUsers] = useState([
        {
            "id": 1,
            "employeeName": "Mr. sys admin",
            "designation": "",
            "modulesAlloted": "Receipts Management, RIDF Loan Processing"
        },
        {
            "id": 2,
            "employeeName": "Mr. <PERSON><PERSON><PERSON>",
            "designation": "Treasury Officer",
            "modulesAlloted": ""
        },
        {
            "id": 3,
            "employeeName": "Mr. <PERSON>iswajit <PERSON>",
            "designation": "",
            "modulesAlloted": ""
        },
        {
            "id": 4,
            "employeeName": "Mr. Consultant",
            "designation": "",
            "modulesAlloted": ""
        },
        {
            "id": 5,
            "employeeName": "Mr. Mohan Chandra Nath",
            "designation": "",
            "modulesAlloted": ""
        },
        {
            "id": 6,
            "employeeName": "Ms. Boby Kalita",
            "designation": "",
            "modulesAlloted": ""
        },
        {
            "id": 7,
            "employeeName": "Mr. Mandeep Das",
            "designation": "Treasury Officer",
            "modulesAlloted": ""
        },
        {
            "id": 8,
            "employeeName": "Mr. BISWAJIT PAUL",
            "designation": "Treasury Officer",
            "modulesAlloted": ""
        },
        {
            "id": 9,
            "employeeName": "Mr. TEST",
            "designation": "",
            "modulesAlloted": ""
        },
        {
            "id": 10,
            "employeeName": "Mrs. DEBASENA BARMAN",
            "designation": "i/c DISTRICT ADULT EDUCATION OFFICER, DHUBRI",
            "modulesAlloted": ""
        },
        {
            "id": 11,
            "employeeName": "Mr. SMT ANTARA SEN, ACS",
            "designation": "Addl Deputy Commissioner",
            "modulesAlloted": ""
        },
        {
            "id": 12,
            "employeeName": "Mr. DEVAJYOTI",
            "designation": "I/C DAEO",
            "modulesAlloted": ""
        },
        {
            "id": 13,
            "employeeName": "Mr. Bhaskar Jyoti Das",
            "designation": "ASSTT. EXECUTIVE ENGINEER (AGRI)",
            "modulesAlloted": ""
        },
        {
            "id": 14,
            "employeeName": "Mr. DIST. AGRICULTURAL OFFICER, DHUBRI",
            "designation": "",
            "modulesAlloted": ""
        },
        {
            "id": 15,
            "employeeName": "Mr. SENIOR AGRICULTURE DEV.OFFICER(MARKETING)DIPHU",
            "designation": "",
            "modulesAlloted": ""
        }
    ]);


    const [modules, setModules] = useState([
        {
            "id": 1,
            "moduleName": "Budget Planning and Preparation",
            "description": "Budget Planning and Preparation",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 2,
            "moduleName": "Budget Allocation and Distribution",
            "description": "Budget Allocation and Distribution",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 3,
            "moduleName": "Administrative Approvals, Technical and Financial Sanctions",
            "description": "Administrative Approvals, Technical and Financial Sanctions",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 4,
            "moduleName": "Accounting and Reconciliation",
            "description": "Accounting and Reconciliation",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 5,
            "moduleName": "RIDF Loan Processing",
            "description": "RIDF Loan Processing",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 6,
            "moduleName": "Helpdesk Management System",
            "description": "Helpdesk Management System",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 7,
            "moduleName": "Reports Generation",
            "description": "Reports Generation",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 8,
            "moduleName": "Debt Management",
            "description": "Debt Management",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 9,
            "moduleName": "Bill Creation",
            "description": "Bill Creation",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 10,
            "moduleName": "Expenditure Processing and Reporting",
            "description": "Expenditure Processing and Reporting",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 11,
            "moduleName": "Cash Planning and Management",
            "description": "Cash Planning and Management",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 12,
            "moduleName": "Employee and Payroll Module",
            "description": "Employee and Payroll Module",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 13,
            "moduleName": "Asset Registry",
            "description": "Asset Registry",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 14,
            "moduleName": "Audit",
            "description": "Audit",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 15,
            "moduleName": "Stock Management",
            "description": "Stock Management",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 16,
            "moduleName": "Receipts Management",
            "description": "Receipts Management",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 17,
            "moduleName": "General Requirements",
            "description": "General Requirements",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 18,
            "moduleName": "Works Account",
            "description": "Works Account",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 19,
            "moduleName": "Digital Signature(DSC)",
            "description": "Digital Signature(DSC)",
            "type": "U",
            "status": "Deactive",
            "deactiveDate": "2024-05-01"
        },
        {
            "id": 20,
            "moduleName": "E-Sign",
            "description": "E-Sign",
            "type": "U",
            "status": "Deactive",
            "deactiveDate": "2025-04-15"
        },
        {
            "id": 21,
            "moduleName": "NIDA Loan Processing",
            "description": "NIDA Loan Processing",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 22,
            "moduleName": "Mobile Application",
            "description": "Mobile Application",
            "type": "U",
            "status": "Active"
        }
    ])

    const [searchQuery, setSearchQuery] = useState('');



   

    const filteredUsers = users.filter(user => {
        const matchesSearch = (
            user.employeeName.toLowerCase().includes(searchQuery.toLowerCase())

        );

        return matchesSearch;
    });


    const indexOfLastUser = currentPage * entriesPerPage;
    const indexOfFirstUser = indexOfLastUser - entriesPerPage;
    const currentUsers = filteredUsers.slice(indexOfFirstUser, indexOfLastUser);
    const totalPages = Math.ceil(filteredUsers.length / entriesPerPage);

    const handlePageChange = (pageNumber) => {
        setCurrentPage(pageNumber);
    };

    const handleEntriesChange = (e) => {
        setEntriesPerPage(Number(e.target.value));
        setCurrentPage(1);
    };


    const filteredModules = modules.filter(module =>
        module.moduleName.toLowerCase().includes(searchQueryModules.toLowerCase())
    );

    const indexOfLastModule = currentPageModules * entriesPerPageModules;
    const indexOfFirstModule = indexOfLastModule - entriesPerPageModules;
    const currentModules = filteredModules.slice(indexOfFirstModule, indexOfLastModule);
    const totalPagesModules = Math.ceil(filteredModules.length / entriesPerPageModules);

    const handlePageChangeModules = (pageNumber) => {
        setCurrentPageModules(pageNumber);
    };

    const handleEntriesChangeModules = (e) => {
        setEntriesPerPageModules(Number(e.target.value));
        setCurrentPageModules(1);
    };

    const headers = [
        { key: 'index', label: '#' },
        { key: 'employeeName', label: 'Employee Name' },
        { key: 'designation', label: 'Designation' },
        { key: 'modulesAlloted', label: 'Modules Alloted' },
        { key: 'actions', label: '' },
    ];

    const moduleHeaders = [
        { key: 'index', label: '#' },
        { key: 'moduleName', label: 'Module Name' },
        { key: 'actions', label: '' },

    ];

    const handleCheckboxChange = (module) => {
        setSelectedModules((prev) => {
          let updatedModules;
          if (prev.some((item) => item.id === module.id)) {
            updatedModules = prev.filter((item) => item.id !== module.id); 
          } else {
            updatedModules = [...prev, module]; 
          }
    
        
          if (selectedUser) {
            setUsers((prevUsers) =>
              prevUsers.map((user) =>
                user.id === selectedUser.id
                  ? {
                      ...user,
                      modulesAlloted: updatedModules.map((m) => m.moduleName).join(', '),
                    }
                  : user
              )
            );
          }
    
          return updatedModules;
        });
      };
    
      const handleEdit = (user) => {
        setSelectedUser(user);
      
        const allocatedModules = user.modulesAlloted
          ? user.modulesAlloted
              .split(', ')
              .map((name) => modules.find((m) => m.moduleName === name.trim()))
              .filter(Boolean)
          : [];
        setSelectedModules(allocatedModules);
        setShowModal(true);
      };

    return (
        <div className="bg-gray-100 font-sans min-h-screen p-4">
            <div className="container mx-auto">
                <div className="flex flex-wrap justify-between items-center mb-4">
                    <h2 className="text-xl font-bold text-gray-700">Privilege</h2>
                    {/* <button
                        className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
                        onClick={() => setShowModal(true)}
                    >
                        Add New
                    </button> */}
                </div>

                {showModal && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                        <div className="bg-white rounded-lg w-full max-w-2xl ">
                        <div className='bg-green-600 p-2 flex justify-between text-white'> 
                        <h3 className="text-lg font-bold mb-4  m-2 text-white ">Allot Model</h3>
                        <button type='button' onClick={() => setShowModal(false)}><span className='font-bold text-2xl m-2'>×</span></button>
                        </div>
                            <div className="flex flex-col md:flex-row gap-4 mb-4 m-2">
                                <input
                                    type="text"
                                    placeholder="Search users..."
                                    className="w-full md:w-1/3 p-2 border rounded"
                                    value={searchQueryModules}
                                    onChange={(e) => searchQueryModules(e.target.value)}
                                />
                            </div>
                            <div>
                                <UserTable
                                    headers={moduleHeaders}
                                    data={currentModules.map((module, index) => ({
                                        ...module,
                                        index: indexOfFirstUser + index + 1,
                                        actions: (
                                            <div className="flex items-center">
                                              <input
                                                type="checkbox"
                                                checked={selectedModules.some((m) => m.id === module.id)}
                                                onChange={() => handleCheckboxChange(module)}
                                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                              />
                                            </div>
                                          ),
                                        }))}
                                />
                            </div>
                            <div className="flex justify-between items-center mt-4 p-4">
                                <div>
                                    <label>Show</label>
                                    <select
                                        className="w-full md:w-24 p-1.5 border rounded text-sm"
                                        value={entriesPerPageModules}
                                        onChange={handleEntriesChangeModules}
                                    >
                                        <option value={15}>15</option>
                                        <option value={20}>20</option>
                                        <option value={30}>30</option>
                                    </select>
                                </div>
                                <span>Showing {indexOfFirstModule + 1} to {Math.min(indexOfLastModule, filteredModules.length)} of {filteredModules.length} entries</span>
                                <div className="flex space-x-2">
                                    <button
                                        className="px-3 py-1 border rounded disabled:opacity-50"
                                        onClick={() => handlePageChangeModules(currentPageModules - 1)}
                                        disabled={currentPageModules === 1}
                                    >
                                        Previous
                                    </button>
                                    {Array.from({ length: totalPagesModules }, (_, i) => i + 1).map(page => (
                                        <button
                                            key={page}
                                            className={`px-3 py-1 border rounded ${currentPageModules === page ? 'bg-green-600 text-white' : ''}`}
                                            onClick={() => handlePageChangeModules(page)}
                                        >
                                            {page}
                                        </button>
                                    ))}
                                    <button
                                        className="px-3 py-1 border rounded disabled:opacity-50"
                                        onClick={() => handlePageChangeModules(currentPageModules + 1)}
                                        disabled={currentPageModules === totalPagesModules}
                                    >
                                        Next
                                    </button>
                                </div>
                            </div>
                        </div>

                    </div>
                )}

                <div className="flex flex-col md:flex-row gap-4 mb-4">
                    <input
                        type="text"
                        placeholder="Search..."
                        className="w-full md:w-1/3 p-2 border rounded"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                    />


                </div>

                <div className="overflow-x-auto">
                    <UserTable
                        headers={headers}
                        data={currentUsers.map((user, index) => ({
                            ...user,
                            index: indexOfFirstUser + index + 1,
                            actions: (
                                <div className="flex space-x-2">
                                    <button
                                        className="text-green-600 "
                                        onClick={() => handleEdit(user)}
                                        title="Edit"
                                    >
                                        <i className="fa fa-plus-square"></i>
                                    </button>

                                </div>
                            ),
                        }))}
                    />

                </div>
                <div className="flex justify-between items-center mt-4">
                    <div>
                        <label>Show</label>
                        <select
                            className="w-full md:w-24 p-1.5 border rounded text-sm"
                            value={entriesPerPage}
                            onChange={handleEntriesChange}
                        >
                            <option value={15}>15</option>
                            <option value={20}>20</option>
                            <option value={30}>30</option>
                        </select>
                    </div>
                    <span>Showing {indexOfFirstUser + 1} to {Math.min(indexOfLastUser, filteredUsers.length)} of {filteredUsers.length} entries</span>
                    <div className="flex space-x-2">
                        <button
                            className="px-3 py-1 border rounded disabled:opacity-50"
                            onClick={() => handlePageChange(currentPage - 1)}
                            disabled={currentPage === 1}
                        >
                            Previous
                        </button>
                        {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                            <button
                                key={page}
                                className={`px-3 py-1 border rounded ${currentPage === page ? 'bg-green-600 text-white' : ''}`}
                                onClick={() => handlePageChange(page)}
                            >
                                {page}
                            </button>
                        ))}
                        <button
                            className="px-3 py-1 border rounded disabled:opacity-50"
                            onClick={() => handlePageChange(currentPage + 1)}
                            disabled={currentPage === totalPages}
                        >
                            Next
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};



export default Privilege
