import  { useState } from 'react';
import UserTable from './UserTableComponent';

const ModuleSetup = () => {
    const [showModal, setShowModal] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [entriesPerPage, setEntriesPerPage] = useState(15);
    const [editUserId, setEditUserId] = useState(null);
    const [showActivateModal, setShowActivateModal] = useState(false);
    const [selectedModuleId, setSelectedModuleId] = useState(null);


    const [formData, setFormData] = useState({
        moduleName: '',
        moduleLabel: '',
        description: '',
        moduleLink: '',
        moduleType: '',
        version: '',
        activeDate: '',
        deactiveDate: '',
        type: "",
        status: ''
    });

    const [modules, setModules] = useState([
        {
            "id": 1,
            "moduleName": "Budget Planning and Preparation",
            "description": "Budget Planning and Preparation",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 2,
            "moduleName": "Budget Allocation and Distribution",
            "description": "Budget Allocation and Distribution",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 3,
            "moduleName": "Administrative Approvals, Technical and Financial Sanctions",
            "description": "Administrative Approvals, Technical and Financial Sanctions",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 4,
            "moduleName": "Accounting and Reconciliation",
            "description": "Accounting and Reconciliation",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 5,
            "moduleName": "RIDF Loan Processing",
            "description": "RIDF Loan Processing",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 6,
            "moduleName": "Helpdesk Management System",
            "description": "Helpdesk Management System",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 7,
            "moduleName": "Reports Generation",
            "description": "Reports Generation",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 8,
            "moduleName": "Debt Management",
            "description": "Debt Management",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 9,
            "moduleName": "Bill Creation",
            "description": "Bill Creation",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 10,
            "moduleName": "Expenditure Processing and Reporting",
            "description": "Expenditure Processing and Reporting",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 11,
            "moduleName": "Cash Planning and Management",
            "description": "Cash Planning and Management",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 12,
            "moduleName": "Employee and Payroll Module",
            "description": "Employee and Payroll Module",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 13,
            "moduleName": "Asset Registry",
            "description": "Asset Registry",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 14,
            "moduleName": "Audit",
            "description": "Audit",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 15,
            "moduleName": "Stock Management",
            "description": "Stock Management",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 16,
            "moduleName": "Receipts Management",
            "description": "Receipts Management",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 17,
            "moduleName": "General Requirements",
            "description": "General Requirements",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 18,
            "moduleName": "Works Account",
            "description": "Works Account",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 19,
            "moduleName": "Digital Signature(DSC)",
            "description": "Digital Signature(DSC)",
            "type": "U",
            "status": "Deactive",
            "deactiveDate": "2024-05-01"
        },
        {
            "id": 20,
            "moduleName": "E-Sign",
            "description": "E-Sign",
            "type": "U",
            "status": "Deactive",
            "deactiveDate": "2025-04-15"
        },
        {
            "id": 21,
            "moduleName": "NIDA Loan Processing",
            "description": "NIDA Loan Processing",
            "type": "U",
            "status": "Active"
        },
        {
            "id": 22,
            "moduleName": "Mobile Application",
            "description": "Mobile Application",
            "type": "U",
            "status": "Active"
        }
    ])

    const [searchQuery, setSearchQuery] = useState('');




    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, [name]: value }));
    };


    const handleSubmit = (e) => {
        e.preventDefault();
        const today = new Date();
        const formattedDate = today.toISOString().split('T')[0];
        console.log(formData, { formattedDate }, "formdata");

        if (editUserId) {
            setModules(
                modules.map((user) =>
                    user.id === editUserId
                        ? {
                            ...user,
                            moduleName: formData.moduleName,
                            moduleLabel: formData.moduleLabel,
                            description: formData.description,
                            moduleLink: formData.moduleLink,
                            moduleType: formData.moduleType,
                            type: "U",
                            version: formData.version,
                            status: formData.deactiveDate > formattedDate ? "Active" : "Deactive",
                            activeDate: formData.activeDate,
                            deactiveDate: formData.deactiveDate,
                        }
                        : user
                )
            );
            setEditUserId(null);
        } else {
            const newId = modules.length > 0 ? Math.max(...modules.map((user) => user.id)) + 1 : 1;
            const newUser = {
                id: newId,
                moduleName: formData.moduleName,
                moduleLabel: formData.moduleLabel,
                description: formData.description,
                moduleLink: formData.moduleLink,
                moduleType: formData.moduleType,
                type: "U",
                version: formData.version,
                status: formData.deactiveDate > formattedDate ? "Active" : "Deactive",
                activeDate: formData.activeDate,
                deactiveDate: formData.deactiveDate,
            };
            setModules([...modules, newUser]);
        }
        setFormData({ moduleName: '', description: '', status: '' });
        setShowModal(false);
    };

    const handleEdit = (user) => {
        setEditUserId(user.id);
        setFormData({
            moduleName: user.moduleName,
            moduleLabel: user.moduleLabel,
            description: user.description,
            moduleLink: user.moduleLink,
            moduleType: user.moduleType,
            type: "U",
            version: user.version,
            activeDate: user.activeDate,
            deactiveDate: user.deactiveDate,
        });
        setShowModal(true);
    };

    const handleDelete = (userId) => {
        if (window.confirm('Are you sure you want to delete this user?')) {
            setModules(modules.filter((user) => user.id !== userId));
        }
    };

    const filteredUsers = modules.filter(user => {
        const matchesSearch = (
            user.moduleName?.toLowerCase().includes(searchQuery.toLowerCase())

        );

        return matchesSearch;
    });


    const indexOfLastUser = currentPage * entriesPerPage;
    const indexOfFirstUser = indexOfLastUser - entriesPerPage;
    const currentUsers = filteredUsers.slice(indexOfFirstUser, indexOfLastUser);
    const totalPages = Math.ceil(filteredUsers.length / entriesPerPage);

    const handlePageChange = (pageNumber) => {
        setCurrentPage(pageNumber);
    };

    const handleEntriesChange = (e) => {
        setEntriesPerPage(Number(e.target.value));
        setCurrentPage(1);
    };

    const handleActivate = (moduleId) => {
        setSelectedModuleId(moduleId);
        setShowActivateModal(true);
    };

    const confirmActivate = () => {
        setModules(modules.map(module =>
            module.id === selectedModuleId
                ? { ...module, status: 'Active' }
                : module
        ));
        setShowActivateModal(false);
        setSelectedModuleId(null);
    };

    // const isDeactiveDateFuture = (deactiveDate) => {
    //     if (!deactiveDate) return false;
    //     const today = new Date();
    //     const formattedDate = today.toISOString().split('T')[0];

    //     const deactive = new Date(deactiveDate);

    //     return formattedDate < deactive;
    // };


    
    const headers = [
        { key: 'index', label: '#' },
        { key: 'moduleName', label: 'Module' },
        { key: 'description', label: 'Description' },
        { key: 'type', label: 'Type' },
        { key: 'status', label: 'Status' },
        { key: 'actions', label: '' },
    ];




    return (
        <div className="bg-gray-100 font-sans min-h-screen p-4">
            <div className="container mx-auto">
                <div className="flex flex-wrap justify-between items-center mb-4">
                    <h2 className="text-xl font-bold text-gray-700">Module Setup </h2>
                    <button
                        className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
                        onClick={() => {
                            setEditUserId(null);
                            setFormData({ moduleName: '', description: '', status: '' });
                            setShowModal(true);
                        }}
                    >
                        Add New
                    </button>
                </div>

                {showModal && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                        <div className="bg-white  rounded-lg w-full max-w-2xl">
                        <div className='bg-green-600 p-2 flex justify-between text-white'> 
                        <h3 className="text-lg font-bold mb-4  m-2 text-white ">Module Setup</h3>
                        <button type='button' onClick={() => setShowModal(false)}><span className='font-bold text-2xl m-2'>×</span></button>
                        </div>
                            <div className='p-6'>
                          
                            <form onSubmit={handleSubmit}>
                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Module Name  <span className="text-red-500">*</span> </label>
                                        <input
                                            type="text"
                                            name="moduleName"
                                            value={formData.moduleName}
                                            onChange={handleInputChange}
                                            required
                                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm p-2 border"
                                            placeholder="Enter module name"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Module Label  <span className="text-red-500">*</span></label>
                                        <input
                                            type="text"
                                            name="moduleLabel"
                                            value={formData.moduleLabel}
                                            onChange={handleInputChange}
                                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm p-2 border"
                                            placeholder="Enter module label"
                                        />
                                    </div>
                                </div>

                                <div className="mb-4">
                                    <label className="block text-sm font-medium text-gray-700">Description  <span className="text-red-500">*</span></label>
                                    <textarea
                                        name="description"
                                        value={formData.description}
                                        onChange={handleInputChange}
                                        required
                                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm p-2 border"
                                        placeholder="Enter description"
                                        rows="2"
                                    />
                                </div>


                                <div className="mb-4">
                                    <label className="block text-sm font-medium text-gray-700">Module Link</label>
                                    <input
                                        type="text"
                                        name="moduleLink"
                                        value={formData.moduleLink}
                                        onChange={handleInputChange}
                                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm p-2 border"
                                        placeholder="Enter module link (optional)"
                                    />
                                </div>


                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Module Type  <span className="text-red-500">*</span></label>
                                        <select
                                            name="moduleType"
                                            value={formData.moduleType}
                                            onChange={handleInputChange}
                                            required
                                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm p-2 border"
                                        >
                                            <option value="user">User</option>
                                            <option value="admin">Admin</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Version  <span className="text-red-500">*</span></label>
                                        <input
                                            type="text"
                                            name="version"
                                            value={formData.version}
                                            onChange={handleInputChange}
                                            required
                                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm p-2 border"
                                            placeholder="Enter version (e.g., 1.0.0)"
                                        />
                                    </div>
                                </div>


                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Active Date  <span className="text-red-500">*</span></label>
                                        <input
                                            type="date"
                                            name="activeDate"
                                            value={formData.activeDate}
                                            onChange={handleInputChange}
                                            required
                                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm p-2 border"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Deactive Date  <span className="text-red-500">*</span></label>
                                        <input
                                            type="date"
                                            name="deactiveDate"
                                            value={formData.deactiveDate}
                                            onChange={handleInputChange}
                                            required
                                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm p-2 border"
                                        />
                                    </div>
                                </div>

                                <div className="mt-6 flex justify-end gap-4">
                                    {/* <button
                                        type="button"
                                        className="px-4 py-2 border rounded"
                                        onClick={() => setShowModal(false)}
                                    >
                                        Cancel
                                    </button> */}
                                    <button
                                        type="submit"
                                        className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                                    >
                                        {editUserId ? 'Update' : 'Save'}
                                    </button>
                                </div>
                            </form>
                            </div>
                        </div>
                    </div>
                )}
                {showActivateModal && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                        <div className="bg-white p-6 rounded-lg w-full max-w-md">
                            <h3 className="text-lg font-bold mb-4">Activate Module</h3>
                            <p className="mb-4">Do you want to activate this module?</p>
                            <div className="flex justify-end gap-4">
                                <button
                                    type="button"
                                    className="px-4 py-2 border rounded"
                                    onClick={() => setShowActivateModal(false)}
                                >
                                    Cancel
                                </button>
                                <button
                                    type="button"
                                    className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                                    onClick={confirmActivate}
                                >
                                    OK
                                </button>
                            </div>
                        </div>
                    </div>
                )}

                <div className="flex flex-col md:flex-row gap-4 mb-4">
                    <input
                        type="text"
                        placeholder="Search..."
                        className="w-full md:w-1/3 p-2 border rounded"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                    />


                </div>

                <div className="overflow-x-auto">
                    <UserTable
                        headers={headers}
                        data={currentUsers.map((user, index) => ({
                            ...user,
                            index: indexOfFirstUser + index + 1,
                            actions: (
                                <div className="flex space-x-2">
                                    {user.status !== "Active" ? (
                                        <button
                                            className="text-green-600 hover:text-green-800"
                                            onClick={() => handleActivate(user.id)}
                                            title="Activate"
                                        >
                                            <i className="fas fa-check-square"></i>
                                        </button>
                                    ) : (
                                        <>
                                            <button
                                                className="text-blue-600 hover:text-blue-800"
                                                onClick={() => handleEdit(user)}
                                                title="Edit"
                                            >
                                                <i className="fas fa-edit"></i>
                                            </button>
                                            <button
                                                className="text-red-600 hover:text-red-800"
                                                onClick={() => handleDelete(user.id)}
                                                title="Delete"
                                            >
                                                <i className="fas fa-trash-alt"></i>
                                            </button>
                                        </>
                                    )}
                                </div>
                            ),
                        }))}
                    />
                </div>
                <div className="flex justify-between items-center mt-4">
                    <div>
                        <label>Show</label>
                        <select
                            className="w-full md:w-24 p-1.5 border rounded text-sm"
                            value={entriesPerPage}
                            onChange={handleEntriesChange}
                        >
                            <option value={15}>15</option>
                            <option value={20}>20</option>
                            <option value={30}>30</option>
                        </select>
                    </div>
                    <span>Showing {indexOfFirstUser + 1} to {Math.min(indexOfLastUser, filteredUsers.length)} of {filteredUsers.length} entries</span>
                    <div className="flex space-x-2">
                        <button
                            className="px-3 py-1 border rounded disabled:opacity-50"
                            onClick={() => handlePageChange(currentPage - 1)}
                            disabled={currentPage === 1}
                        >
                            Previous
                        </button>
                        {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                            <button
                                key={page}
                                className={`px-3 py-1 border rounded ${currentPage === page ? 'bg-green-600 text-white' : ''}`}
                                onClick={() => handlePageChange(page)}
                            >
                                {page}
                            </button>
                        ))}
                        <button
                            className="px-3 py-1 border rounded disabled:opacity-50"
                            onClick={() => handlePageChange(currentPage + 1)}
                            disabled={currentPage === totalPages}
                        >
                            Next
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};



export default ModuleSetup
