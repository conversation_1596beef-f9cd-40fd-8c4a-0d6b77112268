import { useState } from "react";
import UserTableComponent from "./UserTableComponent";

const ExternalUserApproval = () => {
 
      const [currentPage, setCurrentPage] = useState(1);
      const [entriesPerPage, setEntriesPerPage] = useState(15);
      const [searchQuery, setSearchQuery] = useState('');

 const [users, setUsers] = useState([
        // { id: 1, name: "Mr. <PERSON><PERSON>", pan: "**********", email: "<EMAIL>", mobile: "*********", post: "Senior Accounts Officer", office: "AG (AE) & Technology Department" },
        // { id: 2, name: "<PERSON><PERSON>, IAS", pan: "**********", email: "<EMAIL>", mobile: "*********", post: "Director", office: "Information & Technology Department" },
        // { id: 3, name: "Mr. <PERSON><PERSON>, SM (Retd)", pan: "**********", email: "<EMAIL>", mobile: "**********", post: "Director", office: "General Administration Department" },
        // { id: 4, name: "Mr. Dipak <PERSON> Baidya", pan: "ABPB9620F", email: "<EMAIL>", mobile: "**********", post: "Supervisor", office: "AG (AE)" },
        // { id: 5, name: "Mr. Krishna Basi Das", pan: "ACBDP048A", email: "<EMAIL>", mobile: "*********", post: "Senior Accounts Officer", office: "AG (AE)" },
        // { id: 6, name: "Mr. Ratna Biswas", pan: "ABXPB972A", email: "<EMAIL>", mobile: "*********", post: "Assistant Supervisor", office: "AG (AE)" },
        // { id: 7, name: "Mr. Soumen Sarkar", pan: "**********", email: "<EMAIL>", mobile: "**********", post: "Supervisor", office: "AG (AE)" }
    ]);


    const filteredUsers = users.filter(user => {
      const matchesSearch = (
          user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          user.pan.toLowerCase().includes(searchQuery.toLowerCase()) ||
          user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
          user.mobile.includes(searchQuery) ||
          user.post.toLowerCase().includes(searchQuery.toLowerCase()) ||
          user.office.toLowerCase().includes(searchQuery.toLowerCase())
      );
     
      return matchesSearch ;
  });


  const indexOfLastUser = currentPage * entriesPerPage;
  const indexOfFirstUser = indexOfLastUser - entriesPerPage;
  const currentUsers = filteredUsers.slice(indexOfFirstUser, indexOfLastUser);
  const totalPages = Math.ceil(filteredUsers.length / entriesPerPage);

  const handlePageChange = (pageNumber) => {
      setCurrentPage(pageNumber);
  };

  const handleEntriesChange = (e) => {
      setEntriesPerPage(Number(e.target.value));
      setCurrentPage(1);
  };

  const headers = [
      { key: 'index', label: '#' },
      { key: 'name', label: 'User Name' },
      { key: 'pan', label: 'PAN' },
      { key: 'email', label: 'Email' },
      { key: 'mobile', label: 'Mobile No' },
      { key: 'post', label: 'Post' },
      { key: 'Status', label: 'status' },
   
      
  ];

  return (
    <div className="bg-gray-100 font-sans min-h-screen p-4">
    <div className="container mx-auto">
        <div className="flex flex-wrap justify-between items-center mb-4">
            <h2 className="text-xl font-bold text-gray-700"> External User Setup for Approval </h2>
           
        </div>

      

        <div className="flex flex-col md:flex-row gap-4 mb-4">
            <input
                type="text"
                placeholder="Search..."
                className="w-full md:w-1/3 p-2 border rounded"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
            />
          
           
        </div>

        <div className="overflow-x-auto">
            <UserTableComponent
                headers={headers}
                data={currentUsers.map((user, index) => ({
                    ...user,
                    index: indexOfFirstUser + index + 1,
                  
                    }))}
            />
        </div>
        <div className="flex justify-between items-center mt-4">
            <div>
           <label>Show</label>
            <select
                className="w-full md:w-24 p-1.5 border rounded text-sm"
                value={entriesPerPage}
                onChange={handleEntriesChange}
            >
                <option value={15}>15</option>
                <option value={20}>20</option>
                <option value={30}>30</option>
            </select>
            </div>
            <span>Showing {indexOfFirstUser + 1} to {Math.min(indexOfLastUser, filteredUsers.length)} of {filteredUsers.length} entries</span>
            <div className="flex space-x-2">
                <button
                    className="px-3 py-1 border rounded disabled:opacity-50"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                >
                    Previous
                </button>
                {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                    <button
                        key={page}
                        className={`px-3 py-1 border rounded ${currentPage === page ? 'bg-green-600 text-white' : ''}`}
                        onClick={() => handlePageChange(page)}
                    >
                        {page}
                    </button>
                ))}
                <button
                    className="px-3 py-1 border rounded disabled:opacity-50"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                >
                    Next
                </button>
            </div>
        </div>
    </div>
</div>
  )
}

export default ExternalUserApproval
