import {  createApi } from "@reduxjs/toolkit/query/react";
import {baseQueryWithErrorHandling} from '../../../../shared/redux/api/sharedBaseQuery'

export const departmentApi = createApi({
    reducerPath: 'departmentApi',
    baseQuery: baseQueryWithErrorHandling,  
    endpoints: (builder) => ({
        getDepartments: builder.query({
            query: () => '/general-service/departments',
            keepUnusedDataFor: 3600,
        }),
        getDirectorates: builder.query({
            query: (id) => `/general-service/directorates?deptId=${id}`
         })
    })
})

export const {useGetDepartmentsQuery, useGetDirectoratesQuery} = departmentApi;