import {  createApi } from "@reduxjs/toolkit/query/react";
import {baseQueryWithErrorHandling} from '../../../../shared/redux/api/sharedBaseQuery'

// const baseURL = import.meta.env.VITE_API_BASE_URL || "";
export const typeSubTypeApi = createApi({
    reducerPath: 'typeSubTypeApi',
    // baseQuery: fetchBaseQuery({baseUrl: baseURL}),
    baseQuery: baseQueryWithErrorHandling, 
    endpoints: (builder) =>({
        // Endpoint to get the audit type list
        getAuditType: builder.query({
            query: () => '/audit-service/getAuditTypeList',
            keepUnusedDataFor: 3600,
        }),
         // Endpoint to get the audit sub type list by audit type ID
         getAuditSubType: builder.query({
            query: (id) => `/audit-service/getAuditSubTypeList?id=${id}`
         })
    })
}) 

export const {useGetAuditTypeQuery, useGetAuditSubTypeQuery} = typeSubTypeApi;