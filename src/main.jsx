import ReactDOM from 'react-dom/client';
import App from './App';
import { Provider } from "react-redux";
import { BrowserRouter } from 'react-router-dom';
import store from './app/store'

// Access the PUBLIC_URL from Vite's environment variables
const basename = import.meta.env.VITE_PUBLIC_URL || '/';


ReactDOM.createRoot(document.getElementById('root')).render(
    <Provider store={store}>
      <BrowserRouter basename={basename}>
        <App />
      </BrowserRouter>
    </Provider>
);
