import React, { Fragment, useEffect, useState } from "react";
import { Link } from "react-router-dom";

import {
  useReactTable,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  flexRender
} from '@tanstack/react-table';

import { rankItem } from '@tanstack/match-sorter-utils';
import { ChevronLeft, ChevronRight } from "lucide-react";

// Column Filter
const Filter = ({ column, table }) => {
  const columnFilterValue = column.getFilterValue();

  return (
    <>
      <DebouncedInput
        type="text"
        value={columnFilterValue || ''}
        onChange={value => column.setFilterValue(value)}
        placeholder="Search..."
        className="w-36 border shadow rounded"
        list={column.id + 'list'}
      />
      <div className="h-1" />
    </>
  );
};

// Global Filter
const DebouncedInput = ({
  value: initialValue,
  onChange,
  debounce = 500,
  ...props
}) => {
  const [value, setValue] = useState(initialValue);

  useEffect(() => {
    setValue(initialValue);
  }, [initialValue]);

  useEffect(() => {
    const timeout = setTimeout(() => {
      onChange(value);
    }, debounce);

    return () => clearTimeout(timeout);
  }, [debounce, onChange, value]);

  return (
    <input {...props} value={value} onChange={e => setValue(e.target.value)} />
  );
};

const TableContainer = ({
  columns,
  data,
  tableclassName,
  theadclassName,
  divclassName,
  trclassName,
  thclassName,
  tdclassName,
  tbodyclassName,
  isTfoot,
  isSelect,
  isPagination,
  customPageSize,
  isGlobalFilter,
  PaginationClassName,
  SearchPlaceholder
}) => {
  const [columnFilters, setColumnFilters] = useState([]);
  const [globalFilter, setGlobalFilter] = useState('');

  const fuzzyFilter = (row, columnId, value, addMeta) => {
    const itemRank = rankItem(row.getValue(columnId), value);
    addMeta({
      itemRank
    });
    return itemRank.passed;
  };

  const table = useReactTable({
    columns,
    data,
    filterFns: {
      fuzzy: fuzzyFilter,
    },
    state: {
      columnFilters,
      globalFilter,
    },
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    globalFilterFn: fuzzyFilter,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel()
  });

  const {
    getHeaderGroups,
    getFooterGroups,
    getRowModel,
    getPageOptions,
    setPageIndex,
    setPageSize,
    getState,
    getCanPreviousPage,
    getCanNextPage,
    nextPage,
    previousPage,
  } = table;

  useEffect(() => {
    if (customPageSize) setPageSize(Number(customPageSize));
  }, [customPageSize, setPageSize]);

  return (
    <Fragment>
      <div className="grid grid-cols-12 lg:grid-cols-12 gap-3">
        {isSelect && (
          <div className="self-center col-span-12 lg:col-span-6">
            <label>Show
              <select
                name="basic_tables_length"
                className="px-3 py-2 form-select border-slate-200 dark:border-zink-500 focus:outline-none focus:border-custom-500 disabled:bg-slate-100 dark:disabled:bg-zink-600"
                onChange={(event) => setPageSize(event.target.value)}
              >
                <option value="10">10</option>
                <option value="25">25</option>
                <option value="50">50</option>
                <option value="100">100</option>
              </select>
            </label>
          </div>
        )}

        <div className="self-center col-span-12 lg:col-span-6 lg:place-self-end">
          {isGlobalFilter && (
            <label>Search: <DebouncedInput
              value={globalFilter || ''}
              onChange={value => setGlobalFilter(String(value))}
              className="py-2 pr-4 text-sm text-topbar-item bg-topbar border border-topbar-border rounded pl-2 placeholder:text-slate-400"
              placeholder={SearchPlaceholder}
            />
            </label>
          )}
        </div>
      </div>

      <div className={divclassName}>
        <table className={tableclassName}>
          <thead className={theadclassName}>
            {getHeaderGroups().map(headerGroup => (
              <tr key={headerGroup.id} className={trclassName}>
                {headerGroup.headers.map(header => (
                  <th key={header.id} colSpan={header.colSpan}
                    className={`${header.column.getCanSort()} ${thclassName}`}
                    onClick={header.column.getToggleSortingHandler()}
                  >
                    {header.isPlaceholder ? null : (
                      <Fragment>
                        {flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                        {header.column.getIsSorted() ? (
                          header.column.getIsSorted() === 'asc' ? ' ▲' : ' ▼'
                        ) : null}
                        {header.column.getCanFilter() && (
                          <Filter column={header.column} table={table} />
                        )}
                      </Fragment>
                    )}
                  </th>
                ))}
              </tr>
            ))}
          </thead>

          <tbody className={tbodyclassName}>
            {getRowModel().rows.map(row => (
              <tr key={row.id} className={trclassName}>
                {row.getVisibleCells().map(cell => (
                  <td key={cell.id} className={tdclassName}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>

          {isTfoot && (
            <tfoot>
              {getFooterGroups().map((footer, tfKey) => (
                <tr key={tfKey}>
                  {footer.headers.map((tf, key) => (
                    <th key={key} className="p-3 text-left">
                      {flexRender(tf.column.columnDef.header, tf.getContext())}
                    </th>
                  ))}
                </tr>
              ))}
            </tfoot>
          )}
        </table>
      </div>

      {isPagination && (
        <div className={PaginationClassName}>
          <div className="mb-4 grow md:mb-0">
            <div className="text-slate-500">Showing
              <b> {getState().pagination.pageSize}</b> of
              <b> {data.length}</b> Results
            </div>
          </div>
          <ul className="flex flex-wrap items-center gap-2 shrink-0">
            <li>
              <Link
                to="#!"
                className={`inline-flex items-center justify-center h-8 px-3 border rounded ${!getCanPreviousPage() && "disabled"}`}
                onClick={previousPage}
              >
                <ChevronLeft className="size-4 mr-1 rtl:rotate-180" /> Prev
              </Link>
            </li>
            {getPageOptions().map((item, key) => (
              <li key={key}>
                <Link
                  to="#"
                  className={`inline-flex items-center justify-center size-8 border rounded ${getState().pagination.pageIndex === item && "active"}`}
                  onClick={() => setPageIndex(item)}
                >
                  {item + 1}
                </Link>
              </li>
            ))}
            <li>
              <Link
                to="#!"
                className={`inline-flex items-center justify-center h-8 px-3 border rounded ${!getCanNextPage() && "disabled"}`}
                onClick={nextPage}
              >
                Next <ChevronRight className="size-4 ml-1 rtl:rotate-180" />
              </Link>
            </li>
          </ul>
        </div>
      )}
    </Fragment>
  );
};

export default TableContainer;
