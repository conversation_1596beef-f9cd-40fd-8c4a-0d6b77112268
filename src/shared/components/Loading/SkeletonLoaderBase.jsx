export const SkeletonLoaderBase = ({ showBreadcrumb }) => {
    return (
        <>
            {showBreadcrumb && (
                <div className="flex flex-col gap-2 py-4 md:flex-row md:items-center print:hidden">
                    <div className="grow">
                        <div className="h-5 w-32 bg-slate-200 dark:bg-zink-700 animate-pulse rounded"></div>
                    </div>
                    <ul className="flex items-center gap-2 text-sm font-normal shrink-0">
                        <li className="relative ltr:pr-4 rtl:pl-4">
                            <div className="h-4 w-24 bg-slate-200 dark:bg-zink-700 animate-pulse rounded"></div>
                        </li>
                        <li>
                            <div className="h-4 w-16 bg-slate-200 dark:bg-zink-700 animate-pulse rounded"></div>
                        </li>
                    </ul>
                </div>
            )}

            <div className="grid grid-cols-1 gap-x-5 xl:grid-cols-12">
                <div className="xl:col-span-12">
                    <div className="card">
                        <div className="card-body">
                            <div className="flex items-center">
                                <div className="h-5 w-48 bg-slate-200 dark:bg-zink-700 animate-pulse rounded"></div>
                                <div className="h-10 w-32 ml-auto bg-slate-200 dark:bg-zink-700 animate-pulse rounded"></div>
                            </div>
                        </div>
                        <div className="!py-3.5 card-body border-y border-dashed border-slate-200 dark:border-zink-500">
                            <form>
                                <div className="grid grid-cols-1 gap-5 xl:grid-cols-12">
                                    <div className="relative xl:col-span-2">
                                        <div className="h-10 bg-slate-200 dark:bg-zink-700 animate-pulse rounded"></div>
                                    </div>
                                    <div className="xl:col-span-2">
                                        <div className="h-10 bg-slate-200 dark:bg-zink-700 animate-pulse rounded"></div>
                                    </div>
                                    <div className="xl:col-span-3 xl:col-start-10">
                                        <div className="h-10 w-32 bg-slate-200 dark:bg-zink-700 animate-pulse rounded ml-auto"></div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div className="card-body">
                            <div className="-mx-5 overflow-x-auto">
                                <table className="w-full table-auto whitespace-nowrap">
                                    <thead className="bg-slate-100 text-slate-500 dark:bg-zink-600">
                                        <tr>
                                            <th className="px-3.5 py-2.5">
                                                <div className="h-4 w-12 bg-slate-200 dark:bg-zink-700 animate-pulse rounded"></div> {/* Narrow width for Sl. No. */}
                                            </th>
                                            {[...Array(4)].map((_, idx) => (
                                                <th key={idx} className="px-3.5 py-2.5">
                                                    <div className="h-4 w-36 bg-slate-200 dark:bg-zink-700 animate-pulse rounded"></div> {/* Wider columns */}
                                                </th>
                                            ))}
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {[...Array(10)].map((_, idx) => (
                                            <tr key={idx}>
                                                <td className="px-3.5 py-2.5 border border-slate-200 dark:border-zink-500">
                                                    <div className="h-6 w-12 bg-slate-200 dark:bg-zink-700 animate-pulse rounded"></div> {/* Narrow width for Sl. No. */}
                                                </td>
                                                {[...Array(4)].map((__, colIdx) => (
                                                    <td
                                                        key={colIdx}
                                                        className="px-3.5 py-2.5 border border-slate-200 dark:border-zink-500"
                                                    >
                                                        <div className="h-6 w-36 bg-slate-200 dark:bg-zink-700 animate-pulse rounded"></div> {/* Wider columns */}
                                                    </td>
                                                ))}
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                                <div className="flex items-center justify-between p-4">
                                    {/* Skeleton for Records Info */}
                                    <span className="h-4 w-48 bg-gray-200 dark:bg-zink-700 animate-pulse rounded"></span>

                                    {/* Skeleton for Pagination Controls */}
                                    <div className="flex items-center space-x-2">
                                        {/* First Page Button */}
                                        <div className="h-8 w-20 bg-gray-200 dark:bg-zink-700 animate-pulse rounded"></div>

                                        {/* Previous Page Button */}
                                        <div className="h-8 w-28 bg-gray-200 dark:bg-zink-700 animate-pulse rounded"></div>

                                        {/* Go to Page Input */}
                                        <div className="flex items-center space-x-1">
                                            <div className="h-8 w-16 bg-gray-200 dark:bg-zink-700 animate-pulse rounded"></div>
                                            <div className="h-8 w-14 bg-gray-200 dark:bg-zink-700 animate-pulse rounded"></div>
                                        </div>

                                        {/* Next Page Button */}
                                        <div className="h-8 w-28 bg-gray-200 dark:bg-zink-700 animate-pulse rounded"></div>

                                        {/* Last Page Button */}
                                        <div className="h-8 w-20 bg-gray-200 dark:bg-zink-700 animate-pulse rounded"></div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
};
