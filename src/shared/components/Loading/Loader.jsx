// const Loader = ({ text = "Loading..." }) => {
//     return (
//         <div className="flex h-screen items-center justify-center">
//             <div className={`relative flex items-center justify-center h-40 w-40`}>
//                 {/* Outer Static Circle */}
//                 <div className="absolute inset-0 h- rounded-full border-8 border-gray-300"></div>
//                 {/* Inner Rotating Circular Loader */}
//                 <div
//                     className={`absolute h-36 w-36 animate-spin rounded-full border-8 border-gray-200 border-t-blue-800`}
//                     style={{ animationDuration: "2s" }}
//                 ></div>
//                 {/* Text inside Loader */}
//                 <span className="absolute text-xl font-bold text-blue-800">{text}</span>
//             </div>
//         </div>
//     );
// };

// export default Loader;



import React, { useEffect, useState } from 'react';

const Loader = () => {
  const [activeCircle, setActiveCircle] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveCircle((prev) => {
        if (prev === 4) {
          // Reset to 0 to restart the animation from the first circle
          return 0;
        }
        return prev + 1;
      });
    }, 500); // Update every 500ms

    return () => clearInterval(interval); // Cleanup interval on unmount
  }, []);

  return (
    <div className="flex justify-center items-center min-h-screen">
    <div className="flex space-x-2">
      {[...Array(5)].map((_, index) => (
        <div
          key={index}
          className={`w-4 h-4 rounded-full ${index <= activeCircle ? 'bg-cg-primary' : 'bg-gray-500'}`}
        ></div>
      ))}
    </div></div>
  );
};

export default Loader;

