const NoteSkeletonLoader = () => {
  return (
    <>
      {[...Array(3)].map((_, index) => (
        <div key={index} className="grid grid-cols-1 gap-4 p-4 border border-blue-200 mb-2 rounded-lg animate-pulse">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="md:col-span-2">
              <div className="p-4">
                {/* Placeholder for text */}
                <p className="h-4 bg-slate-300 dark:bg-zinc-700 rounded w-3/4 mb-2"></p>
                <h6 className="h-4 bg-slate-300 dark:bg-zinc-700 rounded w-1/4 mb-4"></h6>

                {/* Placeholder for list */}
                <ul className="flex flex-col gap-2">
                  <li className="h-4 bg-slate-300 dark:bg-zinc-700 rounded w-full"></li>
                  <li className="h-4 bg-slate-300 dark:bg-zinc-700 rounded w-5/6"></li>
                  <li className="h-4 bg-slate-300 dark:bg-zinc-700 rounded w-4/6"></li>
                </ul>

                {/* Placeholder for footer */}
                <div className="flex items-center justify-between gap-3 pt-4 mt-auto">
                  <div className="h-4 bg-slate-300 dark:bg-zinc-700 rounded w-1/3 shrink-0"></div>
                  <p className="h-4 bg-slate-300 dark:bg-zinc-700 rounded w-1/4 shrink-0"></p>
                </div>
              </div>
            </div>
          </div>
        </div>
      ))}
    </>
  );
};

export default NoteSkeletonLoader;
