import React from 'react';
import Tab from '../tab/Tab';
import SimpleBar from 'simplebar-react';

const ViewInspectionLoader = () => {
  return (
    <>
      <div className="container group-data-[content=boxed]:max-w-boxed mx-auto relative">
        <div className="flex gap-5 mt-5">
          <Tab.Container defaultActiveKey="mainChatList">
            <div className="block w-full xl:block xl:w-80 shrink-0 menu-content">
              <div className="h-[calc(100vh_-_theme('spacing.10')_*_6)] xl:min-h-[calc(100vh_-_theme('height.header')_*_2.4)] card xl:h-[calc(100%_-_theme('spacing.5'))]">
                <div className="flex flex-col h-full">
                  <Tab.Content className="tab-content">
                    <Tab.Pane eventKey="mainChatList" className="tab-pane" id="mainChatList">
                      <div className="card-body">
                        <div className="flex items-center gap-3">
                          <div className="w-32 h-4 bg-slate-200 dark:bg-zink-600 animate-pulse rounded"></div>
                          <div className="w-8 h-8 bg-slate-200 dark:bg-zink-600 animate-pulse rounded-full"></div>
                        </div>
                      </div>
                      <SimpleBar className="max-h-[calc(100vh_-_380px)] xl:max-h-[calc(100vh_-_300px)]">
                        <ul className="flex flex-col gap-1" id="chatList">
                          <li className="px-5">
                            <div className="h-3 bg-slate-200 dark:bg-zink-600 animate-pulse rounded w-1/4 mb-1"></div>
                          </li>
                          {/* Skeleton loader for each item in the recentChatslist */}
                          {Array(5).fill(null).map((_, index) => (
                            <React.Fragment key={index}>
                              <li>
                                <div className="flex items-center gap-3 px-5 py-2">
                                  <div className="w-9 h-9 bg-slate-200 dark:bg-zink-600 animate-pulse rounded-full"></div>
                                  <div className="flex-1">
                                    <div className="h-4 bg-slate-200 dark:bg-zink-600 animate-pulse rounded w-3/4 mb-1"></div>
                                    <div className="h-3 bg-slate-200 dark:bg-zink-600 animate-pulse rounded w-1/2"></div>
                                  </div>
                                </div>
                              </li>
                            </React.Fragment>
                          ))}
                          {/* {!recentChatslist.length && (
                      <li className="px-5">
                        <div className="h-3 bg-slate-200 dark:bg-zink-600 animate-pulse rounded w-1/3 mb-1"></div>
                      </li>
                    )} */}
                        </ul>
                      </SimpleBar>
                    </Tab.Pane>
                  </Tab.Content>
                </div>
              </div>
            </div>
          </Tab.Container>

          <div
            id="chartlist"
            className="h-[calc(100vh_-_theme('spacing.10')_*_6)] xl:min-h-[calc(100vh_-_theme('height.header')_*_2.4)] card w-full hidden [&.show]:block [&.active]:xl:block chat-content active"
          >
            <div className="relative flex flex-col h-full">
              <div className="relative dark:bg-zink-600/50 grow">
                <SimpleBar className="h-[calc(100vh_-_410px)] xl:h-[calc(100vh_-_330px)]">
                  <ul className="flex flex-col gap-5 list-none card-body">
                    {Array(5).fill(null).map((_, index) => (
                      <li key={index} className="w-full">
                        <div className="flex gap-5 items-center p-4">
                          <div className="w-10 h-10 bg-slate-200 dark:bg-zink-600 animate-pulse rounded-full"></div>
                          <div className="flex-1">
                            <div className="h-4 bg-slate-200 dark:bg-zink-600 animate-pulse rounded w-3/4 mb-2"></div>
                            <div className="h-3 bg-slate-200 dark:bg-zink-600 animate-pulse rounded w-1/2"></div>
                          </div>
                        </div>
                      </li>
                    ))}
                  </ul>
                </SimpleBar>
              </div>
            </div>
          </div>
        </div>
      </div>

    </>
  );
}

export default ViewInspectionLoader;
