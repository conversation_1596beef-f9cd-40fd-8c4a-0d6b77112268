
const BodyLoader = () => {
    return (
        <div className="grid grid-cols-1 gap-x-5 xl:grid-cols-12">
            <div className="xl:col-span-12">

                <div className=" animate-pulse bg-slate-200 dark:bg-zink-700 h-[calc(100vh_-_theme('spacing.10')_*_6)] xl:min-h-[calc(100vh_-_theme('height.header')_*_2.4)] card xl:h-[calc(100%_-_theme('spacing.5'))]">
                    {/* <div className="h-60"></div>  */}
                </div>
            </div>
        </div>

    );
}

export default BodyLoader;
