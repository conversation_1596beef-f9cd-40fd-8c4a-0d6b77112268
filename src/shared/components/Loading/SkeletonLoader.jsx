const SkeletonLoader = ({ count = 1, gridClasses = "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-0", gridSpan = "xl:col-span-4" }) => {
  return (
    <div className={gridClasses}>
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className={`space-y-2 ${gridSpan}`}>
          {/* Simulated label */}
          <div className="h-4 bg-gray-200 rounded w-1/2 animate-pulse"></div>
          {/* Simulated input box */}
          <div className="w-full h-10 bg-gray-200 rounded-lg animate-pulse"></div>
        </div>
      ))}
    </div>
  );
};

export default SkeletonLoader;
