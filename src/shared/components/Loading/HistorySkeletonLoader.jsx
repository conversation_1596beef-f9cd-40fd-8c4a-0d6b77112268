const HistorySkeletonLoader = () => {
    return (
      <>
        {[...Array(3)].map((_, index) => (
          <div
            key={index}
            className="relative pb-4 dark:border-zink-500"
          >
            {/* Left-right border style */}
            <div className="absolute ltr:border-l-2 rtl:border-r-2 ltr:left-3.5 rtl:right-3.5 before:top-1.5 before:-bottom-1.5"></div>
            <div className="relative flex gap-2">
              {/* Icon Placeholder */}
              <div
                className={`size-8 p-0.5 bg-slate-300 dark:bg-zinc-700 flex items-center justify-center border rounded-full shrink-0 border-slate-200 dark:border-zinc-500 animate-pulse`}
              >
                {/* Placeholder for Icon */}
                <div className="h-6 w-6 bg-slate-200 dark:bg-zinc-600 rounded-full"></div>
              </div>
              
              {/* Text and Content */}
              <div className="flex-1">
                {/* Skeleton loader for status */}
                <h6 className="h-4 bg-slate-300 dark:bg-zinc-700 rounded w-3/4 mb-1 animate-pulse"></h6>
                {/* Skeleton loader for name */}
                <p className="h-4 bg-slate-300 dark:bg-zinc-700 rounded w-1/2 mb-2 animate-pulse"></p>
                {/* Skeleton loader for date */}
                <p className="h-4 bg-slate-300 dark:bg-zinc-700 rounded w-1/3 animate-pulse"></p>
              </div>
            </div>
          </div>
        ))}
      </>
    );
  };
  
  export default HistorySkeletonLoader;
  