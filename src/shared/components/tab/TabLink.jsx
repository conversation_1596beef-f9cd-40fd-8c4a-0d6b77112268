import { Link } from "react-router-dom";

export const TabLink = ({ tabId, activeTab, onClick, label,id }) => {
    const linkTo = id ? `?id=${id}` : "#";
    return (
        <li>
            <Link
                to={linkTo}
                className={`inline-block px-4 py-2 text-base transition-all duration-300 ease-linear rounded-md text-slate-500 dark:text-zink-200 border border-transparent [&.active]:bg-custom-500 dark:[&.active]:bg-custom-500 [&.active]:text-white dark:[&.active]:text-white hover:text-custom-500 dark:hover:text-custom-500 active:text-custom-500 dark:active:text-custom-500 -mb-[1px] ${activeTab === tabId && "active"}`}
                onClick={() => onClick(tabId)}
            >
                {label}
            </Link>
        </li>
    );
};