import { Controller } from "react-hook-form";
import Select from "react-select";
import {getFinancialYears, getCurrentFinYear} from '../../../shared/utils/financialYears'

const FinancialYearSelect = ({ numYears, control, onChange , isRequired = false, selectedYear}) => {
    const financialYears = getFinancialYears(numYears);
    const options = financialYears.map((year) => ({
      value: year,
      label: year,
    }));
    let defaultOption = selectedYear;
    if (!control && defaultOption== null) {
      const currentFinYear = getCurrentFinYear();
       defaultOption = options.find(option => option.value === currentFinYear);
    }
    if (typeof defaultOption === 'string') {
      defaultOption = options.find(option => option.value === defaultOption);
    }

    if (control) {
      // React Hook Form Case
      return (
        <Controller
          name="finYear"
          control={control}
          rules={isRequired ? {required: " Financial Year is required"}:{}}
          render={({ field }) => (
            <Select
              {...field}
              options={options}
              isSearchable={true}
              placeholder="Select a financial year"
              value={options?.find(option => option.value === field.value)}
              onChange={(selectedOption) => field.onChange(selectedOption.value)}
            />
          )}
        />
      );
    }
  
    // Standalone Case
    return (
      <Select
        options={options}
        isSearchable={true}
        defaultValue={defaultOption}
        placeholder="Select a financial year"
        onChange={(selectedOption) => {
          if (onChange) onChange(selectedOption ? selectedOption.value : null);
        }}
      />
    );
  };
  
  export default FinancialYearSelect;