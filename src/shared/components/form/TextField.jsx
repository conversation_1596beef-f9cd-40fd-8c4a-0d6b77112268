const TextField = ({
    label = "",
    name,
    register,
    validation = {},
    error = null,
    multiline = false,
    placeholder = "Enter value",
    labelClass
  }) => {
    const inputClasses =
      "form-input border-slate-200 dark:border-zink-500 focus:outline-none focus:border-custom-500 disabled:bg-slate-100 dark:disabled:bg-zink-600 disabled:border-slate-300 dark:disabled:border-zink-500 dark:disabled:text-zink-200 disabled:text-slate-500 dark:text-zink-100 dark:bg-zink-700 dark:focus:border-custom-800 placeholder:text-slate-400 dark:placeholder:text-zink-200";
    const defaultLabelClass =
      "inline-block mb-2 text-base";

    return (
      <div className="form-group">
        {label && (
          <label
            htmlFor={name}
            className={`${defaultLabelClass} ${labelClass}`.trim()}>
            {label}</label>
        )}
        {multiline ? (
          <textarea
            id={name}
            placeholder={placeholder}
            {...register(name, validation)}
            className={inputClasses}
          />
        ) : (
          <input
            id={name}
            placeholder={placeholder}
            {...register(name, validation)}
            className={inputClasses}
          />
        )}
        {error && <p className="error-text text-red-500 mt-1">{error.message}</p>}
      </div>
    );
  };
  
  export default TextField;
  