// export const ViewItem = ({ label, value, spanClass }) => (
//     <div className={`flex items-center ${spanClass}`}>
//         <span className="w-0.5 h-6 bg-sky-800 mr-2"></span>
//         <div className="flex items-center">
//             <label className="font-bold text-sky-800 min-w-[200px] dark:text-white">{label}</label>
//             <span className="text-gray-700 font-semibold mx-16 dark:text-white">:</span>
//         </div>
//         <p className="text-gray-600 dark:text-white">{value || "-N/A-"}</p>
//     </div>
// );


export const ViewItem = ({ label, value }) => {
    return (
      <div className="flex flex-col sm:flex-row items-start sm:items-center border-b border-gray-300 py-2">
        <label className="font-bold text-sky-800 w-full sm:w-1/3 dark:text-white">
          {label}
        </label>
        <div className="flex items-center flex-1 sm:ml-2">
          {/* Show colon only on larger screens */}
          <span className="hidden sm:inline text-gray-700 font-semibold mr-8 dark:text-white">
            :
          </span>
          <span className="text-gray-600 dark:text-white">{value || "-N/A-"}</span>
        </div>
      </div>
    );
  };
  