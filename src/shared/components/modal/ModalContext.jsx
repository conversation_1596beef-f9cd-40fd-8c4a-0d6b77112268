import { createContext, useContext, useState, useEffect } from 'react';

const ModalContext = createContext(undefined);

export const useModalContext = () => {
  const context = useContext(ModalContext);
  if (context === undefined) {
    throw new Error('useModalContext must be used within a ModalContextProvider');
  }
  return context;
};

export const ModalContextProvider = ({ show, onHide, children }) => {
  const [isModal, setIsModal] = useState(false);

  const handleModalToggle = () => {
    setIsModal(!isModal);
  };

  const bodyElement = document.body;

  useEffect(() => {
    bodyElement.classList.toggle('overflow-hidden');
  }, [isModal, show, bodyElement]);

  return (
    <ModalContext.Provider value={{ isModal, setIsModal, handleModalToggle, show, onHide }}>
      {children}
    </ModalContext.Provider>
  );
};
