import ModalHeader from "./ModalHeader";
import { <PERSON><PERSON><PERSON><PERSON>, ModalFooter, ModalTitle } from "./ModalContent";
import { ModalContextProvider } from "./ModalContext";
import { useEffect } from "react";

const Modal = ({
    show,
    onHide,
    children,
    className,
    placement,
    id,
    dialogClassName,
    as: Component = "div",
    ...props
}) => {

    useEffect(() => {
        if (show) {
            document.body.classList.add('overflow-hidden');
        } else {
            document.body.classList.remove('overflow-hidden');
        }

        // Cleanup on unmount
        return () => {
            document.body.classList.remove('overflow-hidden');
        };
    }, [show]);
    return (
        <>
            <div
                {...props}
                id={id ? id : "defaultModal"}
                className={`${className} ${!show ? "show hidden" : ""}`}
            >
                <ModalContextProvider show={show} onHide={onHide}>
                    <Component className={dialogClassName ? dialogClassName : ""}>
                        {children}
                    </Component>
                </ModalContextProvider>
            </div>
            <div
                onClick={onHide}
                className={`fixed inset-0 bg-slate-900/40 dark:bg-zink-800/70 z-[1049] backdrop-overlay ${!show ? "hidden" : ""}`}
                id="backDropDiv"
            ></div>
        </>
    );
};

export default Object.assign(Modal, {
    Header: ModalHeader,
    Title: ModalTitle,
    Body: ModalBody,
    Footer: ModalFooter,
});
