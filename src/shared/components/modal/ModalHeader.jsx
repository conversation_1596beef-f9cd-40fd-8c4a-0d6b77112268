import { useModalContext } from './ModalContext';
import { X } from 'lucide-react';

const ModalHeader = ({
    children,
    className,
    closeButtonClass,
    as: Component = "div",
    ...props
}) => {
    const { onHide, handleModalToggle } = useModalContext();

    return (
        <Component
            {...props}
            className={className ? className : ''}
            onClick={onHide ? onHide : handleModalToggle}
        >
            {children}
            <button
                data-modal-close="closeModal"
                className={closeButtonClass ? closeButtonClass : ''}
            >
                <X className="lucide lucide-x size-5" />
            </button>
        </Component>
    );
};

export default ModalHeader;
