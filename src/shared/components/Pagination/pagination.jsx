import { useState } from 'react';
import { ArrowLeft, ArrowRight, ChevronsLeft, ChevronsRight } from 'lucide-react';

const Pagination = ({ currentPage, totalPages, totalRecords, onPageChange }) => {
  const [inputValue, setInputValue] = useState(currentPage);

  const handleFirstPage = () => onPageChange(1);
  const handleLastPage = () => onPageChange(totalPages);
  const handlePreviousPage = () => onPageChange(Math.max(currentPage - 1, 1));
  const handleNextPage = () => onPageChange(Math.min(currentPage + 1, totalPages));

  const handleInputChange = (e) => {
    const value = e.target.value;
    // Allow only numeric values or empty string
    if (value === '' || /^\d+$/.test(value)) {
      setInputValue(value);
    }
  };

  const handleGoToPage = () => {
    const page = Number(inputValue);
    if (page > 0 && page <= totalPages) {
      onPageChange(page);
      setInputValue(page); // Update input to reflect the page navigated to
    } else {
      // Optionally, reset input if the page is out of range
      setInputValue(currentPage); // Reset to current page if out of range
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      handleGoToPage();
    }
  };

  return (
    <div className="flex items-center justify-between p-4">
      <span className="text-sm text-gray-700">
        Showing {((currentPage - 1) * 15) + 1} - {Math.min(currentPage * 15, totalRecords)} of {totalRecords} Records
      </span>
      <div className="flex items-center space-x-2">
        <button
          onClick={handleFirstPage}
          disabled={currentPage === 1}
          className={`flex items-center px-3 py-1 border rounded ${currentPage === 1 ? 'bg-gray-200 cursor-not-allowed' : 'bg-blue-500 text-white hover:bg-blue-600'}`}>
          <ChevronsLeft size={16} />
          First
        </button>
        <button
          onClick={handlePreviousPage}
          disabled={currentPage === 1}
          className={`flex items-center px-3 py-1 border rounded ${currentPage === 1 ? 'bg-gray-200 cursor-not-allowed' : 'bg-blue-500 text-white hover:bg-blue-600'}`}>
          <ArrowLeft size={16} />
          Previous
        </button>
        
        {/* Go to Page Input */}
        <div className="flex items-center space-x-1">
          <input
            type="number"
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown} // Use onKeyDown instead of onKeyPress
            className="w-16 px-2 py-1 border rounded text-center"
            min={1}
            max={totalPages}
            placeholder="Page"
          />
          <button
            onClick={handleGoToPage}
            className="px-3 py-1 border rounded bg-blue-500 text-white hover:bg-blue-600">
            Go
          </button>
        </div>
        
        <button
          onClick={handleNextPage}
          disabled={currentPage === totalPages}
          className={`flex items-center px-3 py-1 border rounded ${currentPage === totalPages ? 'bg-gray-200 cursor-not-allowed' : 'bg-blue-500 text-white hover:bg-blue-600'}`}>
          <ArrowRight size={16} />
          Next
        </button>
        <button
          onClick={handleLastPage}
          disabled={currentPage === totalPages}
          className={`flex items-center px-3 py-1 border rounded ${currentPage === totalPages ? 'bg-gray-200 cursor-not-allowed' : 'bg-blue-500 text-white hover:bg-blue-600'}`}>
          <ChevronsRight size={16} />
          Last
        </button>
      </div>
    </div>
  );
};

export default Pagination;
