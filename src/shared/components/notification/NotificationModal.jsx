import React, { useState } from "react";
import { Bell, Edit, Trash, Check } from "lucide-react"; // Import Lucide icons
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css'; // Import Quill styles

const NotificationModal = ({ isOpen, onClose, onSave, editData }) => {
  const [notification, setNotification] = useState(editData?.notification || "");
  const [fromDate, setFromDate] = useState(editData?.fromDate || "");
  const [toDate, setToDate] = useState(editData?.toDate || "");
  const [showNewDate, setShowNewDate] = useState(editData?.showNewDate || "");

  if (!isOpen) return null;

  const handleSave = (e) => {
    e.preventDefault();
    if (notification && fromDate && toDate && showNewDate) {
      onSave({ notification, fromDate, toDate, showNewDate, status: "Active" });
      setNotification("");
      setFromDate("");
      setToDate("");
      setShowNewDate("");
      onClose();
    } else {
      alert("All fields are required!");
    }
  };

  // Define Quill modules and formats
  const modules = {
                  toolbar: [
      [{ 'header': [1, 2, 3, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'color': [] }, { 'background': [] }],
      ['link'],
      ['clean']
    ],
};

  const formats = [
    'header',
    'bold', 'italic', 'underline', 'strike',
    'list', 'bullet',
    'color', 'background',
    'link'
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-2xl">
        <div className="bg-green-600 p-2 flex justify-between text-white">
          <div className="flex items-center space-x-2 m-2">
            <Bell className="w-5 h-5" />
            <h3 className="text-lg font-bold text-white">Notification Setup</h3>
        </div>
                <button
                  type="button"
            onClick={onClose}
            className="font-bold text-2xl m-2"
                >
            ×
                </button>
              </div>

        <div className="p-6">
          <form onSubmit={handleSave}>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Notification <span className="text-red-500">*</span>
              </label>
              <div className="border rounded">
                <ReactQuill
                  value={notification}
                  onChange={setNotification}
                  modules={modules}
                  formats={formats}
                  theme="snow"
                  style={{ height: '200px', marginBottom: '50px' }}
                />
        </div>
          </div>

            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-4 mt-16">
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  From Date <span className="text-red-500">*</span>
                </label>
                <input
                  type="date"
                  name="fromDate"
                  value={fromDate}
                  onChange={(e) => setFromDate(e.target.value)}
                  required
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm p-2 border"
                />
          </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  To Date <span className="text-red-500">*</span>
                </label>
                <input
                  type="date"
                  name="toDate"
                  value={toDate}
                  onChange={(e) => setToDate(e.target.value)}
                  required
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm p-2 border"
                />
      </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Show New <span className="text-red-500">*</span>
                </label>
                <input
                  type="date"
                  name="showNewDate"
                  value={showNewDate}
                  onChange={(e) => setShowNewDate(e.target.value)}
                  required
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm p-2 border"
                />
    </div>
            </div>

            <div className="mt-6 flex justify-end gap-4">
              <button
                type="submit"
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
              >
                {editData ? "Update" : "Save"} Notification
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

const NotificationTable = ({ notifications, headers, onDelete, onEdit, onActivate }) => (
  <div className="overflow-x-auto">
    <table className="min-w-full bg-white border">
      <thead>
        <tr className="bg-gray-200">
          {headers.map((header) => (
            <th key={header.key} className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              {header.label}
            </th>
          ))}
        </tr>
      </thead>
      <tbody>
        {notifications.map((notif) => (
          <tr key={notif.id} className="border-t hover:bg-gray-50">
            <td className="px-4 py-2">{notif.index}</td>
            <td className="px-4 py-2" dangerouslySetInnerHTML={{ __html: notif.notification }}></td>
            <td className="px-4 py-2">{notif.fromDate}</td>
            <td className="px-4 py-2">{notif.toDate}</td>
            <td className="px-4 py-2">{notif.showNewDate}</td>
            <td className="px-4 py-2">{notif.status}</td>
            <td className="px-4 py-2">
              <div className="flex space-x-2">
                {notif.status !== "Active" ? (
                  <button
                    className="text-green-600 hover:text-green-800"
                    onClick={() => onActivate(notif.id)}
                    title="Activate"
                  >
                    <Check className="w-5 h-5" />
                  </button>
                ) : (
                  <>
                    <button
                      className="text-blue-600 hover:text-blue-800"
                      onClick={() => onEdit(notif.id)}
                      title="Edit"
                    >
                      <Edit className="w-5 h-5" />
                    </button>
                    <button
                      className="text-red-600 hover:text-red-800"
                      onClick={() => onDelete(notif.id)}
                      title="Delete"
                    >
                      <Trash className="w-5 h-5" />
                    </button>
                  </>
                )}
              </div>
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  </div>
);

const Notification = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [notifications, setNotifications] = useState([
    {
      id: 1,
      notification: "<p>Welcome to our platform! Please <strong>complete your profile</strong>.</p>",
      fromDate: "2023-10-01",
      toDate: "2023-12-31",
      showNewDate: "2023-10-01",
      status: "Active",
    },
    {
      id: 2,
      notification: "<p>System maintenance scheduled for this weekend. <em>Services may be unavailable</em>.</p>",
      fromDate: "2023-11-15",
      toDate: "2023-11-20",
      showNewDate: "2023-11-15",
      status: "Active",
    },
    {
      id: 3,
      notification: "<p>New features have been added to the dashboard. <a href='#'>Click here</a> to learn more.</p>",
      fromDate: "2023-09-15",
      toDate: "2023-10-15",
      showNewDate: "2023-09-15",
      status: "Deactive",
      deactiveDate: "2023-10-15",
    },
  ]);
  const [currentPage, setCurrentPage] = useState(1);
  const [entriesPerPage, setEntriesPerPage] = useState(15);
  const [editNotificationId, setEditNotificationId] = useState(null);
  const [showActivateModal, setShowActivateModal] = useState(false);
  const [selectedNotificationId, setSelectedNotificationId] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [editData, setEditData] = useState(null);

  const handleSave = (notifData) => {
    if (editNotificationId) {
      setNotifications(
        notifications.map((notif) =>
          notif.id === editNotificationId ? { ...notif, ...notifData } : notif
        )
      );
      setEditNotificationId(null);
    } else {
      const newId =
        notifications.length > 0
          ? Math.max(...notifications.map((notif) => notif.id)) + 1
          : 1;
      setNotifications([...notifications, { id: newId, ...notifData }]);
    }
  };

  const handleEdit = (notificationId) => {
    const notificationToEdit = notifications.find(
      (notif) => notif.id === notificationId
    );
    if (notificationToEdit) {
      setEditNotificationId(notificationId);
      setEditData(notificationToEdit);
      setIsModalOpen(true);
    }
  };

  const handleDelete = (notificationId) => {
    if (window.confirm("Are you sure you want to delete this notification?")) {
      setNotifications(
        notifications.filter((notif) => notif.id !== notificationId)
      );
    }
  };

  const handleActivate = (notificationId) => {
    setSelectedNotificationId(notificationId);
    setShowActivateModal(true);
  };

  const confirmActivate = () => {
    setNotifications(
      notifications.map((notif) =>
        notif.id === selectedNotificationId
          ? { ...notif, status: "Active" }
          : notif
      )
    );
    setShowActivateModal(false);
    setSelectedNotificationId(null);
  };

  // Search in HTML content by converting to plain text first
  const filteredNotifications = notifications.filter((notif) => {
    // Create a temporary element to extract text from HTML
    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = notif.notification;
    const textContent = tempDiv.textContent || tempDiv.innerText || "";

    return textContent.toLowerCase().includes(searchQuery.toLowerCase());
  });

  const indexOfLastNotification = currentPage * entriesPerPage;
  const indexOfFirstNotification = indexOfLastNotification - entriesPerPage;
  const currentNotifications = filteredNotifications.slice(
    indexOfFirstNotification,
    indexOfLastNotification
  );
  const totalPages = Math.ceil(filteredNotifications.length / entriesPerPage);

  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  const handleEntriesChange = (e) => {
    setEntriesPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  const headers = [
    { key: "index", label: "#" },
    { key: "notification", label: "Notification" },
    { key: "fromDate", label: "From Date" },
    { key: "toDate", label: "To Date" },
    { key: "showNewDate", label: "Show New" },
    { key: "status", label: "Status" },
    { key: "actions", label: "Actions" },
  ];

  return (
    <div className="bg-gray-100 font-sans min-h-screen p-4">
      <div className="container mx-auto">
        <div className="flex flex-wrap justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-700">
            Notification Management
          </h2>
          <button
            className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
            onClick={() => {
              setEditNotificationId(null);
              setEditData(null);
              setIsModalOpen(true);
            }}
          >
            Add Notification
          </button>
        </div>

        <NotificationModal
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false);
            setEditData(null);
          }}
          onSave={handleSave}
          editData={editData}
        />

        {showActivateModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
            <div className="bg-white p-6 rounded-lg w-full max-w-md">
              <h3 className="text-lg font-bold mb-4">Activate Notification</h3>
              <p className="mb-4">Do you want to activate this notification?</p>
              <div className="flex justify-end gap-4">
                <button
                  type="button"
                  className="px-4 py-2 border rounded"
                  onClick={() => setShowActivateModal(false)}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                  onClick={confirmActivate}
                >
                  OK
                </button>
              </div>
            </div>
          </div>
        )}

        <div className="flex flex-col md:flex-row gap-4 mb-4">
          <input
            type="text"
            placeholder="Search..."
            className="w-full md:w-1/3 p-2 border rounded"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <NotificationTable
          notifications={currentNotifications.map((notif, index) => ({
            ...notif,
            index: indexOfFirstNotification + index + 1,
          }))}
          headers={headers}
          onDelete={handleDelete}
          onEdit={handleEdit}
          onActivate={handleActivate}
        />

        <div className="flex justify-between items-center mt-4">
          <div>
            <label>Show</label>
            <select
              className="w-full md:w-24 p-1.5 border rounded text-sm"
              value={entriesPerPage}
              onChange={handleEntriesChange}
            >
              <option value={15}>15</option>
              <option value={20}>20</option>
              <option value={30}>30</option>
            </select>
          </div>
          <span>
            Showing {indexOfFirstNotification + 1} to{" "}
            {Math.min(indexOfLastNotification, filteredNotifications.length)} of{" "}
            {filteredNotifications.length} entries
          </span>
          <div className="flex space-x-2">
            <button
              className="px-3 py-1 border rounded disabled:opacity-50"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
            >
              Previous
            </button>
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <button
                key={page}
                className={`px-3 py-1 border rounded ${
                  currentPage === page ? "bg-green-600 text-white" : ""
                }`}
                onClick={() => handlePageChange(page)}
              >
                {page}
              </button>
            ))}
            <button
              className="px-3 py-1 border rounded disabled:opacity-50"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Notification;
