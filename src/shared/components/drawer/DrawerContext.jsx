import { createContext, useContext } from 'react';

const DrawerContext = createContext(undefined);

export const useDrawerContext = () => {
  const context = useContext(DrawerContext);
  if (context === undefined) {
    throw new Error('useDrawerContext must be used within a DrawerContextProvider');
  }
  return context;
};

export const DrawerContextProvider = ({ show, onHide, children }) => {
  return (
    <DrawerContext.Provider value={{ show, onHide }}>
      {children}
    </DrawerContext.Provider>
  );
};
