import { useDrawerContext } from "./DrawerContext";

const DrawerHeader = ({ children, className, closeButtonClass, as: Component = "button", ...props }) => {
    const { onHide } = useDrawerContext();

    return (
        <Component
            className={className ? className : ''}
            onClick={onHide}
            {...props}
        >
            {children}
        </Component>
    );
}

export default DrawerHeader;
