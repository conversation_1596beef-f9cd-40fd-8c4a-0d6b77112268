import React, { useState, useEffect, useCallback, useMemo, Fragment } from "react";
import { debounce } from "lodash-es";
import { Tree } from "react-arborist";
import {
  PlusCircle,
  ArrowLeftRight,
  X,
  ChevronDown,
  ChevronRight,
  User,
  Building2,
  Briefcase,
  Users,
  Search,
  ChevronsDown,
  ChevronsUp,
  RefreshCw,
  AlertCircle,
  FolderOpen,
} from "lucide-react";
import { data } from "./heirarchyData";
import "remixicon/fonts/remixicon.css";
import hierarchyService from "../../../domains/smartadmin/services/hierarchyService";

/* ----------------- MODAL COMPONENT ----------------- */
const Modal = ({ isOpen, onClose, title, children, onOk, footer }) => {
  if (!isOpen) return null;
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-gray-800">{title}</h3>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded-full"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>
        <div className="mb-4">{children}</div>
        {footer
          ? footer
          : onOk && (
              <div className="flex justify-end space-x-2">
                <button
                  onClick={onClose}
                  className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-md"
                >
                  Cancel
                </button>
                <button
                  onClick={onOk}
                  className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                >
                  Confirm
                </button>
              </div>
            )}
      </div>
    </div>
  );
};

/* ----------------- ICON HELPER ----------------- */
const NodeIcon = ({ isLeaf, category, onClick }) => {
  // Get the appropriate icon based on category
  const getIcon = () => {
    switch (category) {
      case "O":
        return <Building2 className="w-5 h-5 text-purple-600" />; // Office
      case "D":
        return <Briefcase className="w-5 h-5 text-blue-600" />; // Department
      case "R":
        return <Briefcase className="w-5 h-5 text-green-600" />; // Directorate
      case "T":
        return <Briefcase className="w-5 h-5 text-yellow-600" />; // Treasury
      case "S":
        return <User className="w-5 h-5 text-red-600" />; // Seat
      case "G":
        return <Briefcase className="w-5 h-5 text-teal-600" />; // Finance
      default:
        // If no valid category but leaf is "Y", default to Seat icon
        if (isLeaf === "Y") return <User className="w-5 h-5 text-red-600" />; // Seat
        // Default icon for unknown types
        return <Building2 className="w-5 h-5 text-gray-600" />; // Default to Building icon
    }
  };

  // If no click handler, just return the icon
  if (!onClick) return getIcon();

  // Return clickable wrapper if onClick is provided
  return (
    <div
      onClick={(e) => {
        e.stopPropagation();
        onClick(e);
      }}
      className="cursor-pointer hover:bg-gray-100 rounded-full p-1"
      title="Click to toggle expand/collapse"
    >
      {getIcon()}
    </div>
  );
};

/* ----------------- NODE TYPE HELPER ----------------- */
const getNodeType = (category) => {
  // Always use category to determine node type
  switch (category) {
    case "O":
      return "Office";
    case "D":
      return "Department";
    case "R":
      return "Directorate";
    case "T":
      return "Treasury";
    case "S":
      return "Seat";
    case "G":
      return "Finance";
    default:
      return "Office"; // Default to Office instead of Unknown
  }
};

/* ----------------- MAIN COMPONENT ----------------- */
export default function HierarchyBrowser() {
  const [treeData, setTreeData] = useState([]);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [newNodeName, setNewNodeName] = useState("");
  const [newNodeCode, setNewNodeCode] = useState("");
  const [newNodeType, setNewNodeType] = useState("S"); // Default to Seat
  const [selectedNodeId, setSelectedNodeId] = useState(null);
  const [parentNodeDetails, setParentNodeDetails] = useState(null);
  const [loadingParentDetails, setLoadingParentDetails] = useState(false);
  const [isExchangeModalOpen, setIsExchangeModalOpen] = useState(false);
  const [exchangeNodeId, setExchangeNodeId] = useState(null);
  // Removed unused searchTerm state
  const [globalSearchTerm, setGlobalSearchTerm] = useState("");
  const [highlightedNodeIds, setHighlightedNodeIds] = useState([]);
  const [treeInstance, setTreeInstance] = useState(null);
  const [allExpanded, setAllExpanded] = useState(false);
  const [treeReady, setTreeReady] = useState(false);
  const [loadingNodes, setLoadingNodes] = useState({});
  const [nodesWithLoadedChildren, setNodesWithLoadedChildren] = useState(
    new Set()
  );
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isExpandingAll, setIsExpandingAll] = useState(false);

  // User assignment states
  const [isUserAssignModalOpen, setIsUserAssignModalOpen] = useState(false);
  const [selectedNode, setSelectedNode] = useState(null);
  const [availableUsers, setAvailableUsers] = useState([]);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [nodesWithAssignedUsers, setNodesWithAssignedUsers] = useState({});
  const [userSearchTerm, setUserSearchTerm] = useState("");

  // Admin assignment states
  const [isAdminAssignModalOpen, setIsAdminAssignModalOpen] = useState(false);
  const [selectedAdminUsers, setSelectedAdminUsers] = useState([]);
  const [nodesWithAssignedAdmins, setNodesWithAssignedAdmins] = useState({});
  const [adminSearchTerm, setAdminSearchTerm] = useState("");

  // Post selection state for Seat category
  const [selectedPost, setSelectedPost] = useState(null);
  const [postSearchTerm, setPostSearchTerm] = useState("");
  const [filteredPostings, setFilteredPostings] = useState([]);

  // Additional state for posting details
  const [postingDetailsLoaded, setPostingDetailsLoaded] = useState(false);
  const [postingDetailsError, setPostingDetailsError] = useState(null);

  // Track manually collapsed nodes
  const [manuallyCollapsedNodes, setManuallyCollapsedNodes] = useState(
    new Set()
  );

  // Exchange node search states
  const [exchangeSearchTerm, setExchangeSearchTerm] = useState("");
  const [selectedCategoryFilter, setSelectedCategoryFilter] = useState("");
  const [filteredExchangeNodes, setFilteredExchangeNodes] = useState([]);

  // Fetch root-level hierarchy data from backend
  const fetchRootHierarchy = async () => {

    setIsLoading(true);
    setError(null);

    // Reset state for a clean refresh
    setAllExpanded(false);
    setNodesWithLoadedChildren(new Set());
    setHighlightedNodeIds([]);
    setGlobalSearchTerm("");

    try {
      // Call the hierarchyService to fetch root hierarchy
      const response = await hierarchyService.fetchRootHierarchy();


      if (response) {
        // Transform data to match tree structure expected by Arborist
        const transformedData = Array.isArray(response)
          ? response.map((item) => ({
              id: item.hierarchyId.toString(),
              name: item.hierarchyName || "Unnamed",
              code: item.hierarchyCode || "",
              activeStatus: item.activeStatus || "Y",
              hierarchyLevel: item.hierarchyLevel,
              leaf: item.leaf,
              category: item.category, // Store the category code directly (O, D, R, T, S, G)
              type: getNodeType(item.category), // Get display name based on category
              isLeaf: item.leaf === "Y", // Seat nodes are also leaf nodes
              children: [], // Initially empty, will be loaded on demand
            }))
          : [];


        setTreeData(transformedData);

        // If we have a tree instance, close all nodes to reset the view
        if (treeInstance && typeof treeInstance.closeAll === "function") {
          setTimeout(() => {
            try {
              treeInstance.closeAll();

            } catch (err) {

            }
          }, 100);
        }
      } else {

        setError("Failed to fetch hierarchy data");
        // Fallback to sample data for development
        setTreeData(data);
      }
    } catch (error) {

      setError("Error loading hierarchy data. Please try again later.");
      // Fallback to sample data for development
      setTreeData(data);
    } finally {
      setIsLoading(false);

    }
  };

  // Load initial hierarchy on component mount
  useEffect(() => {
    fetchRootHierarchy();
  }, []);

  // Memoized flatten tree function with caching
  const flattenTree = useCallback((nodes) => {
    const result = [];
    const queue = new Set(nodes);

    while (queue.size) {
      const node = queue.values().next().value;
      queue.delete(node);

      if (node && node.name) {
        result.push(node);
        if (node.children) {
          node.children.forEach((child) => queue.add(child));
        }
      }
    }

    return result;
  }, []);

  // Check if a node has children ready to display
  const hasLoadedChildren = (nodeId) => {
    return nodesWithLoadedChildren.has(nodeId.toString());
  };

  const getChildrenDetailByParentId = async (parentId) => {
    try {
      setLoadingNodes((prev) => ({ ...prev, [parentId]: true }));


      // Use the hierarchyService to fetch children
      const response = await hierarchyService.fetchChildrenByParentId(parentId);

      if (response) {
        return response;
      } else {

        return [];
      }
    } catch (error) {

      return [];
    } finally {
      setLoadingNodes((prev) => ({ ...prev, [parentId]: false }));
    }
  };

  // Function to load children and update tree - optimized for large datasets
  const loadChildrenForNode = async (
    nodeId,
    autoLoadNonLeafChildren = false,
    ancestorIds = []
  ) => {
    // Convert nodeId to string for consistency
    const nodeIdStr = nodeId.toString();

    // Prevent duplicate loading
    if (loadingNodes[nodeIdStr] || hasLoadedChildren(nodeIdStr)) {
      return;
    }

    // Track ancestor IDs to prevent loops
    const currentAncestorIds = [...ancestorIds, nodeIdStr];

    try {
      // Mark as loading
      setLoadingNodes((prev) => ({ ...prev, [nodeIdStr]: true }));

      // Get children data from API
      const childrenData = await getChildrenDetailByParentId(nodeIdStr);

      if (!Array.isArray(childrenData)) {
        setLoadingNodes((prev) => ({ ...prev, [nodeIdStr]: false }));
        return [];
      }

      // Filter out any children that would create loops
      const filteredChildren = childrenData.filter((child) => {
        const childId = child.hierarchyId.toString();
        return !currentAncestorIds.includes(childId);
      });

      // Log only if significant filtering occurred
      if (childrenData.length !== filteredChildren.length) {

      }

      // Map children to tree format
      const mappedChildren = filteredChildren.map((child) => ({
        id: child.hierarchyId.toString(),
        name: child.hierarchyName || "Unnamed",
        code: child.hierarchyCode || "",
        activeStatus: child.activeStatus || "Y",
        hierarchyLevel: child.hierarchyLevel,
        leaf: child.leaf,
        category: child.category,
        type: getNodeType(child.category),
        parentId: child.parentHierarchy,
        isLeaf: child.leaf === "Y",
        children: [],
      }));

      // Update tree data efficiently
      setTreeData((prevData) => {
        const updateTree = (nodes) => {
          return nodes.map((node) => {
            if (node.id === nodeIdStr) {
              return { ...node, children: mappedChildren };
            } else if (node.children?.length > 0) {
              return { ...node, children: updateTree(node.children) };
            }
            return node;
          });
        };
        return updateTree(prevData);
      });

      // Mark node as having loaded children
      setNodesWithLoadedChildren((prev) => new Set([...prev, nodeIdStr]));

      // Open the node if we have a tree instance
      if (treeInstance && !isExpandingAll) {
        const node = treeInstance.get(nodeIdStr);
        if (node) node.open();
      }

      // Clear loading state
      setLoadingNodes((prev) => ({ ...prev, [nodeIdStr]: false }));

      // Recursively load children if requested
      if (autoLoadNonLeafChildren) {
        const nonLeafChildren = mappedChildren.filter(
          (child) => child.leaf !== "Y"
        );

        if (nonLeafChildren.length > 0) {
          // Process in batches
          const BATCH_SIZE = 10;
          for (let i = 0; i < nonLeafChildren.length; i += BATCH_SIZE) {
            const batch = nonLeafChildren.slice(i, i + BATCH_SIZE);

            // Load children in parallel
            await Promise.all(
              batch.map((child) =>
                loadChildrenForNode(child.id, true, currentAncestorIds)
              )
            );

            // Small delay between batches
            if (i + BATCH_SIZE < nonLeafChildren.length) {
              await new Promise((resolve) => setTimeout(resolve, 50));
            }
          }
        }
      }

      return mappedChildren;
    } catch (error) {

      setLoadingNodes((prev) => ({ ...prev, [nodeIdStr]: false }));
      return [];
    }
  };

  // Note: We've moved the node toggle logic directly into the onClick handler and the handleToggle function
  // This ensures that clicking the chevron or the node itself only expands one level at a time

  // Function to load all children recursively - optimized for large datasets
  const loadAllChildrenRecursively = useCallback(
    async (startNodeIds = []) => {


      // Keep track of nodes we've processed to avoid duplicates
      const processedNodeIds = new Set();

      // Add a maximum depth limit to prevent infinite recursion
      const MAX_DEPTH = 10;
      let currentDepth = 0;

      // Maximum number of nodes to process to prevent browser freezing
      const MAX_NODES = 5000;
      let processedCount = 0;

      // Queue of node IDs to process
      let queue = [...startNodeIds];

      // Batch size for processing - larger batches for better performance with large datasets
      const BATCH_SIZE = 100;

      // Process nodes level by level (breadth-first)
      while (
        queue.length > 0 &&
        currentDepth < MAX_DEPTH &&
        processedCount < MAX_NODES
      ) {
        currentDepth++;


        // Get the current batch of nodes
        const allNodesInLevel = [...queue];
        queue = []; // Clear the queue for the next level

        // Process nodes in batches to avoid overwhelming the browser
        for (
          let i = 0;
          i < allNodesInLevel.length && processedCount < MAX_NODES;
          i += BATCH_SIZE
        ) {
          const currentBatch = allNodesInLevel.slice(i, i + BATCH_SIZE);


          // Process all nodes in the current batch in parallel
          const loadPromises = currentBatch.map(async (nodeId) => {
            // Skip if already processed or if we've reached the maximum
            if (
              processedNodeIds.has(nodeId.toString()) ||
              processedCount >= MAX_NODES
            ) {
              return;
            }

            // Mark as processed and increment counter
            processedNodeIds.add(nodeId.toString());
            processedCount++;

            // Skip leaf nodes
            const node = treeInstance?.get(nodeId);
            if (!node || node?.data?.isLeaf) {
              return;
            }

            // Load children if not already loaded
            if (!hasLoadedChildren(nodeId)) {
              try {
                // Pass the list of processed node IDs as ancestors to prevent loops
                const ancestors = Array.from(processedNodeIds);
                const children = await loadChildrenForNode(
                  nodeId,
                  true,
                  ancestors
                );

                // Add non-leaf children to the queue for the next level
                if (Array.isArray(children) && children.length > 0) {
                  const childIds = children
                    .filter((child) => !child.isLeaf)
                    .map((child) => child.id);

                  // Only add children that haven't been processed yet
                  const newChildIds = childIds.filter(
                    (id) => !processedNodeIds.has(id.toString())
                  );
                  queue.push(...newChildIds);
                }
              } catch (error) {

              }
            } else {
              // If children already loaded, still add non-leaf children to queue
              // But only if they haven't been processed yet
              try {
                const node = treeInstance?.get(nodeId);
                if (node?.children && node.children.length > 0) {
                  const childIds = node.children
                    .filter(
                      (child) =>
                        child?.data &&
                        !child.data.isLeaf &&
                        !processedNodeIds.has(child.id.toString())
                    )
                    .map((child) => child.id);

                  queue.push(...childIds);
                }
              } catch (error) {

              }
            }
          });

          try {
            // Wait for the current batch to complete before moving to the next
            await Promise.all(loadPromises);
          } catch (error) {

          }

          // Only add a small delay between batches, not between each node
          if (i + BATCH_SIZE < allNodesInLevel.length) {
            await new Promise((resolve) => setTimeout(resolve, 10));
          }
        }

        // Smaller delay between levels to allow UI updates but keep processing fast
        await new Promise((resolve) => setTimeout(resolve, 20));
      }

      if (processedCount >= MAX_NODES) {

      } else if (currentDepth >= MAX_DEPTH) {

      }


    },
    [treeInstance, hasLoadedChildren, loadChildrenForNode]
  );

  // Handle expanding/collapsing all nodes
  const toggleExpandAll = useCallback(() => {
    if (!treeReady || !treeInstance || isExpandingAll) {

      return;
    }

    try {


      if (allExpanded) {
        // Close all nodes

        setIsExpandingAll(true);

        // Ensure we have a valid tree instance before calling methods on it
        if (treeInstance && typeof treeInstance.closeAll === "function") {
          treeInstance.closeAll();

        } else {

        }

        // Reset manually collapsed nodes since we're collapsing everything
        setManuallyCollapsedNodes(new Set());

        setAllExpanded(false);
        setIsExpandingAll(false);
      } else {
        // Simpler and more immediate approach to expand all nodes
        const expandAll = async () => {
          setIsExpandingAll(true);

          try {


            // First, try to use the built-in openAll method for immediate feedback
            if (treeInstance && typeof treeInstance.openAll === "function") {

              treeInstance.openAll();
            }

            // Then load all children for all visible nodes that are now open


            // Get all visible nodes that are now open
            const visibleNodes = treeInstance.visibleNodes || [];
            const nonLeafVisibleNodes = visibleNodes.filter(
              (node) => node && node.data && !node.data.isLeaf && node.isOpen
            );



            // Process in batches to avoid overwhelming the browser
            const BATCH_SIZE = 10;
            for (let i = 0; i < nonLeafVisibleNodes.length; i += BATCH_SIZE) {
              const batch = nonLeafVisibleNodes.slice(i, i + BATCH_SIZE);

              // Load children for this batch in parallel
              await Promise.all(
                batch.map((node) => {
                  // Skip nodes that were manually collapsed
                  if (manuallyCollapsedNodes.has(node.id.toString())) {

                    return Promise.resolve();
                  }

                  // Load children with autoLoadNonLeafChildren=true
                  // Get the path to this node to use as ancestors
                  const nodePath = findNodePath(treeData, node.id);
                  return loadChildrenForNode(node.id, true, nodePath);
                })
              );

              // Small delay between batches
              if (i + BATCH_SIZE < nonLeafVisibleNodes.length) {
                await new Promise((resolve) => setTimeout(resolve, 50));
              }
            }

            // Set expanded state
            setAllExpanded(true);

          } catch (err) {

          } finally {
            setIsExpandingAll(false);
          }
        };

        expandAll();
      }
    } catch (error) {

      setAllExpanded(false);
      setIsExpandingAll(false);
    }
  }, [
    treeReady,
    treeInstance,
    allExpanded,
    hasLoadedChildren,
    loadChildrenForNode,
    isExpandingAll,
    treeData,
    loadAllChildrenRecursively,
    manuallyCollapsedNodes,
  ]);

  // Debounced global search handler
  const debouncedSearch = useMemo(
    () =>
      debounce((term, data, instance) => {
        if (term) {
          const allNodes = flattenTree(data);
          const termLower = term.toLowerCase();
          const matchingNodes = allNodes.filter(
            (n) =>
              n.name.toLowerCase().includes(termLower) ||
              (n.code && n.code.toLowerCase().includes(termLower))
          );
          setHighlightedNodeIds(matchingNodes.map((n) => n.id));

          if (instance && matchingNodes.length > 0) {
            const nodesToExpand = new Set();

            matchingNodes.forEach((node) => {
              const path = findNodePath(data, node.id);
              path.slice(0, -1).forEach((ancestorId) => {
                nodesToExpand.add(ancestorId);
              });
            });

            Array.from(nodesToExpand).forEach((nodeId) => {
              const node = instance.get(nodeId);
              node?.open();
            });
          }
        } else {
          setHighlightedNodeIds([]);
        }
      }, 300),
    [flattenTree]
  );

  useEffect(() => {
    debouncedSearch(globalSearchTerm, treeData, treeInstance);
    return () => debouncedSearch.cancel();
  }, [globalSearchTerm, treeData, treeInstance, debouncedSearch]);

  // Clean up search timeouts when component unmounts
  useEffect(() => {
    return () => {
      if (window.userSearchTimeout) {
        clearTimeout(window.userSearchTimeout);
      }
      if (window.adminSearchTimeout) {
        clearTimeout(window.adminSearchTimeout);
      }
      if (window.postSearchTimeout) {
        clearTimeout(window.postSearchTimeout);
      }
      if (window.exchangeSearchTimeout) {
        clearTimeout(window.exchangeSearchTimeout);
      }
    };
  }, []);

  // Find path to a node (returns array of ancestor IDs including the node)
  const findNodePath = (nodes, targetId, currentPath = []) => {
    // Convert targetId to string for consistent comparison
    const targetIdStr = targetId?.toString() || "";
    if (!targetIdStr) return [];

    for (const node of nodes) {
      if (!node?.id) continue;

      const nodeIdStr = node.id.toString();
      const newPath = [...currentPath, nodeIdStr];

      // Check if this is the target node
      if (nodeIdStr === targetIdStr) {
        return newPath;
      }

      // If this node has children, search them
      if (node.children?.length > 0) {
        const pathInChildren = findNodePath(
          node.children,
          targetIdStr,
          newPath
        );
        if (pathInChildren.length > 0) {
          return pathInChildren;
        }
      }
    }

    return [];
  };

  const handleAddNode = async () => {
    if (!selectedNodeId || !newNodeName.trim() || !newNodeCode.trim()) {
      setIsAddModalOpen(false);
      return;
    }

    // Post validation is now handled inside the if (newNodeType === "S") block below

    try {
      // Create data for API
      const newNodeData = {
        hierarchyId: null,
        hierarchyName: newNodeName,
        hierarchyCode: newNodeCode,
        activeStatus: "Y",
        category: newNodeType, // Using category instead of typeId for the new node types
        parentHierarchy: selectedNodeId,
      };

      // Add post information for Seat category
      if (newNodeType === "S") {
        if (!selectedPost) {
          alert("Please select a post for the Seat node.");
          return;
        }

        // Log the selected post for debugging
        console.log("Adding node with selected post:", selectedPost);

        // Validate the selected post has the required fields
        const postId = selectedPost?.postingId ;

        if (!postId) {
          alert("The selected post is missing required information (name or ID).");
          return;
        }

        // Use the posting details directly from the selected post
        newNodeData.postingId = postId;

        // Add additional post details if available

        // For debugging - log the final node data being sent
        console.log("Final node data with posting details:", newNodeData);
      }

      // Add parent node details if available from API
      if (parentNodeDetails) {
        // Include the entire parent node details in the request
        // The backend will extract what it needs
        newNodeData.parentDetails = parentNodeDetails;
      }



      // Call hierarchyService to create new node
      const response = await hierarchyService.createHierarchyNode(
        newNodeData
      );

      // Check if the response is a success message
      if (response === "saved successfully" || response) {
        // Since the API returns a success message and not the created node object,
        // we need to create the node object ourselves using the data we sent
        const newNode = {
          // Generate a temporary ID - this will be replaced when the node is refreshed
          id: Date.now().toString(), // Temporary ID until refresh
          name: newNodeName,
          code: newNodeCode,
          activeStatus: "Y",
          category: newNodeType, // Store the category code directly
          hierarchyLevel: 1, // Default level
          leaf: newNodeType === "S" ? "Y" : "N", // Seat nodes are leaf nodes
          type: getNodeType(newNodeType), // Get display name based on category
          isLeaf: newNodeType === "S", // Seat nodes are leaf nodes
          post:
            newNodeType === "S"
              ? selectedPost
                ? selectedPost.name
                : null
              : null,
          postId:
            newNodeType === "S"
              ? selectedPost
                ? selectedPost.id
                : null
              : null,
          children: [],
        };

        // Update tree data
        setTreeData((prev) => {
          const addRecursively = (nodes) =>
            nodes.map((node) => {
              if (node.id && node.id.toString() === selectedNodeId.toString()) {
                const children = node.children
                  ? [...node.children, newNode]
                  : [newNode];
                return { ...node, children };
              }
              if (node.children) {
                return { ...node, children: addRecursively(node.children) };
              }
              return node;
            });

          return addRecursively(prev);
        });

        // Mark parent as having loaded children so we don't reload
        setNodesWithLoadedChildren((prev) => {
          const newSet = new Set(prev);
          newSet.add(selectedNodeId.toString());
          return newSet;
        });

        // Show success message
        alert("Hierarchy created successfully!");

        // Refresh the parent node to get the actual node data from the server
        // This ensures we have the correct hierarchyId and other server-generated properties
        try {
          // Mark the parent node as not having loaded children so it will reload
          setNodesWithLoadedChildren((prev) => {
            const newSet = new Set(prev);
            newSet.delete(selectedNodeId.toString());
            return newSet;
          });

          // Load the children for the parent node to get the actual data
          const nodePath = findNodePath(treeData, selectedNodeId);
          loadChildrenForNode(selectedNodeId, false, nodePath);
        } catch (refreshError) {
          console.error("Error refreshing parent node:", refreshError);
          // Continue with the temporary node if refresh fails
        }
      } else {
        alert("Failed to create node. Please try again.");
      }
    } catch (error) {
      console.error("Error creating node:", error);
      alert("Error adding new node. Please try again.");
    }

    // Reset form
    setNewNodeName("");
    setNewNodeCode("");
    setSelectedPost(null);
    setParentNodeDetails(null);
    setPostingDetailsLoaded(false);
    setPostingDetailsError(null);
    setPostSearchTerm("");
    setFilteredPostings([]);
    setIsAddModalOpen(false);
  };

  const handleExchangeNodes = () => {
    if (
      !selectedNodeId ||
      !exchangeNodeId ||
      selectedNodeId === exchangeNodeId
    ) {
      setIsExchangeModalOpen(false);
      return;
    }
    const newData = structuredClone(treeData);
    let nodeA = null,
      parentA = null,
      indexA = -1;
    let nodeB = null,
      parentB = null,
      indexB = -1;
    const findNode = (nodes, parent = null) => {
      nodes.forEach((n, i) => {
        if (n.id && n.id.toString() === selectedNodeId.toString()) {
          nodeA = n;
          parentA = parent;
          indexA = i;
        }
        if (n.id && n.id.toString() === exchangeNodeId.toString()) {
          nodeB = n;
          parentB = parent;
          indexB = i;
        }
        if (n.children) {
          findNode(n.children, n);
        }
      });
    };
    findNode(newData);
    if (nodeA && nodeB) {
      if (parentA && parentA.children) {
        parentA.children[indexA] = nodeB;
      } else {
        const rootIndexA = newData.findIndex(
          (n) => n.id.toString() === nodeA.id.toString()
        );
        if (rootIndexA >= 0) newData[rootIndexA] = nodeB;
      }
      if (parentB && parentB.children) {
        parentB.children[indexB] = nodeA;
      } else {
        const rootIndexB = newData.findIndex(
          (n) => n.id.toString() === nodeB.id.toString()
        );
        if (rootIndexB >= 0) newData[rootIndexB] = nodeA;
      }
    }
    setTreeData(newData);
    setIsExchangeModalOpen(false);
    setSelectedNodeId(null);
    setExchangeNodeId(null);
  };

  // Fetch users from backend with search functionality
  const fetchUsers = async (searchTerm) => {
    setLoadingUsers(true);
    try {


      // Call hierarchyService to search users
      const response = await hierarchyService.searchUsers(searchTerm);



      if (response && Array.isArray(response?.users)) {
        // Map the response data to match our expected format
        const mappedUsers = response?.users.map((user) => {
          // Format the name according to backend getUserFullName() method
          let fullName = "";
          if (user.userName) {
            if (user.titleName) {
              fullName += user.titleName.trim() + " ";
            }
            fullName += user.userName.trim();
            if (user.lastName && user.lastName.trim() !== "") {
              fullName += " " + user.lastName.trim();
            }
          } else {
            fullName = user.name || "Unknown";
          }

          return {
            id: user.id,
            name: fullName,
            titleName: user.titleName || "",
            userName: user.userName || "",
            lastName: user.lastName || "",
            email: user.email || "",
            mobile: user.mobile || "",
            userCode: user.userCode || "",
          };
        });

        setAvailableUsers(mappedUsers);
      } else {

        setAvailableUsers([]);

        // Fallback to mock data for development/testing
        const mockUsers = [
          {
            id: 1,
            titleName: "Mr.",
            userName: "John",
            lastName: "Doe",
            name: "Mr. John Doe",
            email: "<EMAIL>",
            mobile: "1234567890",
            userCode: "U001",
          },
          {
            id: 2,
            titleName: "Mrs.",
            userName: "Jane",
            lastName: "Smith",
            name: "Mrs. Jane Smith",
            email: "<EMAIL>",
            mobile: "2345678901",
            userCode: "U002",
          },
          {
            id: 3,
            titleName: "Dr.",
            userName: "Admin",
            lastName: "",
            name: "Dr. Admin",
            email: "<EMAIL>",
            mobile: "3456789012",
            userCode: "A001",
          },
          {
            id: 4,
            titleName: "",
            userName: "Manager",
            lastName: "User",
            name: "Manager User",
            email: "<EMAIL>",
            mobile: "4567890123",
            userCode: "M001",
          },
          {
            id: 5,
            titleName: "Ms.",
            userName: "Support",
            lastName: "Team",
            name: "Ms. Support Team",
            email: "<EMAIL>",
            mobile: "5678901234",
            userCode: "S001",
          },
        ];

        // Filter mock data based on search term
        const filteredMockUsers = searchTerm
          ? mockUsers.filter((user) => {
              const searchTermLower = searchTerm.toLowerCase();
              return (
                user.name.toLowerCase().includes(searchTermLower) ||
                (user.titleName &&
                  user.titleName.toLowerCase().includes(searchTermLower)) ||
                (user.userName &&
                  user.userName.toLowerCase().includes(searchTermLower)) ||
                (user.lastName &&
                  user.lastName.toLowerCase().includes(searchTermLower)) ||
                user.email.toLowerCase().includes(searchTermLower) ||
                user.mobile.includes(searchTerm) ||
                user.userCode.toLowerCase().includes(searchTermLower)
              );
            })
          : mockUsers;

        setAvailableUsers(filteredMockUsers);
      }
    } catch (error) {


      // Fallback to mock data
      const mockUsers = [
        {
          id: 1,
          titleName: "Mr.",
          userName: "John",
          lastName: "Doe",
          name: "Mr. John Doe",
          email: "<EMAIL>",
          mobile: "1234567890",
          userCode: "U001",
        },
        {
          id: 2,
          titleName: "Mrs.",
          userName: "Jane",
          lastName: "Smith",
          name: "Mrs. Jane Smith",
          email: "<EMAIL>",
          mobile: "2345678901",
          userCode: "U002",
        },
      ];

      // Filter mock data based on search term
      const filteredMockUsers = searchTerm
        ? mockUsers.filter((user) => {
            const searchTermLower = searchTerm.toLowerCase();
            return (
              user.name.toLowerCase().includes(searchTermLower) ||
              (user.titleName &&
                user.titleName.toLowerCase().includes(searchTermLower)) ||
              (user.userName &&
                user.userName.toLowerCase().includes(searchTermLower)) ||
              (user.lastName &&
                user.lastName.toLowerCase().includes(searchTermLower)) ||
              user.email.toLowerCase().includes(searchTermLower) ||
              user.mobile.includes(searchTerm) ||
              user.userCode.toLowerCase().includes(searchTermLower)
            );
          })
        : mockUsers;

      setAvailableUsers(filteredMockUsers);
    } finally {
      setLoadingUsers(false);
    }
  };

  // We no longer need to fetch posts since we only use parent node's posting details
  const fetchPosts = async () => {
    console.log("Post fetching is disabled - using only parent node's posting details");
  };

  // Open user assignment modal
  const openUserAssignModal = (nodeId) => {
    // Find the node in the tree data
    const findNode = (nodes) => {
      for (const node of nodes) {
        if (node.id && node.id.toString() === nodeId.toString()) {
          return node;
        }
        if (node.children && node.children.length > 0) {
          const foundNode = findNode(node.children);
          if (foundNode) return foundNode;
        }
      }
      return null;
    };

    const node = findNode(treeData);
    if (node) {
      // Check if this is a leaf node based on leaf value from backend
      if (node.leaf !== "Y") {
        alert("Only leaf nodes can have users assigned to them.");
        return;
      }

      setSelectedNode(node);
      setSelectedNodeId(nodeId);

      // Get currently assigned user for this node (only one user can be assigned)
      const assignedUsers = nodesWithAssignedUsers[nodeId] || [];
      // Take only the first user if there are any assigned users
      setSelectedUsers(assignedUsers.length > 0 ? [assignedUsers[0]] : []);

      // Reset search term
      setUserSearchTerm("");

      // Fetch available users without search term initially
      fetchUsers("");

      // Open the modal
      setIsUserAssignModalOpen(true);
    }
  };

  // Open admin assignment modal
  const openAdminAssignModal = (nodeId) => {
    // Find the node in the tree data
    const findNode = (nodes) => {
      for (const node of nodes) {
        if (node.id && node.id.toString() === nodeId.toString()) {
          return node;
        }
        if (node.children && node.children.length > 0) {
          const foundNode = findNode(node.children);
          if (foundNode) return foundNode;
        }
      }
      return null;
    };

    const node = findNode(treeData);
    if (node) {
      // Check if this is NOT a seat node (category !== "S")
      if (node.category === "S") {
        alert("Admin users cannot be assigned to Seat nodes.");
        return;
      }

      setSelectedNode(node);
      setSelectedNodeId(nodeId);

      // Get currently assigned admin for this node (only one admin can be assigned)
      const assignedAdmins = nodesWithAssignedAdmins[nodeId] || [];
      // Take only the first admin if there are any assigned admins
      setSelectedAdminUsers(assignedAdmins.length > 0 ? [assignedAdmins[0]] : []);

      // Reset search term
      setAdminSearchTerm("");

      // Fetch available users without search term initially
      fetchUsers("");

      // Open the modal
      setIsAdminAssignModalOpen(true);
    }
  };

  // Handle user assignment - only one user per node
  const handleAssignUsers = async () => {
    if (!selectedNodeId || !selectedNode) {
      setIsUserAssignModalOpen(false);
      return;
    }

    try {
      // Get the single selected user (if any)
      const selectedUser = selectedUsers.length > 0 ? selectedUsers[0] : null;

      // Check if this is a leaf node based on leaf value from backend
      if (selectedNode.leaf !== "Y") {
        alert("Only leaf nodes can have users assigned to them.");
        return;
      }

      // Determine if this is an assignment or unassignment
      const isUnassigning = !selectedUser;

      // Determine the user ID for assignment
      const userId = selectedUser ? selectedUser.id : null;

      // Call the appropriate hierarchyService method
      const response = isUnassigning
        ? await hierarchyService.unassignUserFromNode(selectedNodeId)
        : await hierarchyService.assignUserToNode(
            selectedNodeId,
            userId
          );

      if (response) {
        // Update the local state to reflect assigned user
        // Store only the single user in an array for consistency
        setNodesWithAssignedUsers((prev) => ({
          ...prev,
          [selectedNodeId]: selectedUser ? [selectedUser] : [],
        }));

        // Close the modal
        setIsUserAssignModalOpen(false);
      } else {
        alert(
          `Failed to ${
            isUnassigning ? "unassign" : "assign"
          } user. Please try again.`
        );
      }
    } catch (error) {
      // Even if there's an error, update the UI for better user experience
      // This is a fallback for development/testing
      const userToAssign = selectedUsers.length > 0 ? selectedUsers[0] : null;
      setNodesWithAssignedUsers((prev) => ({
        ...prev,
        [selectedNodeId]: userToAssign ? [userToAssign] : [],
      }));

      // Close the modal
      setIsUserAssignModalOpen(false);

      alert(
        "An error occurred while updating user assignment, but the UI has been updated locally."
      );
    }
  };

  // Handle admin assignment - only one admin per node
  const handleAssignAdmins = async () => {
    if (!selectedNodeId || !selectedNode) {
      setIsAdminAssignModalOpen(false);
      return;
    }

    try {
      // Get the single selected admin (if any)
      const selectedAdmin = selectedAdminUsers.length > 0 ? selectedAdminUsers[0] : null;

      // Check if this is NOT a seat node
      if (selectedNode.category === "S") {
        alert("Admin users cannot be assigned to Seat nodes.");
        return;
      }

      // Determine if this is an assignment or unassignment
      const isUnassigning = !selectedAdmin;

      // Determine the user ID for assignment
      const userId = selectedAdmin ? selectedAdmin.id : null;

      // Call the appropriate hierarchyService method
      const response = isUnassigning
        ? await hierarchyService.unassignAdminFromNode(selectedNodeId)
        : await hierarchyService.assignAdminToNode(
            selectedNodeId,
            userId
          );

      if (response) {
        // Update the local state to reflect assigned admin
        // Store only the single admin in an array for consistency
        setNodesWithAssignedAdmins((prev) => ({
          ...prev,
          [selectedNodeId]: selectedAdmin ? [selectedAdmin] : [],
        }));

        // Close the modal
        setIsAdminAssignModalOpen(false);
      } else {
        alert(
          `Failed to ${
            isUnassigning ? "unassign" : "assign"
          } admin. Please try again.`
        );
      }
    } catch (error) {
      // Even if there's an error, update the UI for better user experience
      // This is a fallback for development/testing
      const adminToAssign = selectedAdminUsers.length > 0 ? selectedAdminUsers[0] : null;
      setNodesWithAssignedAdmins((prev) => ({
        ...prev,
        [selectedNodeId]: adminToAssign ? [adminToAssign] : [],
      }));

      // Close the modal
      setIsAdminAssignModalOpen(false);

      alert(
        "An error occurred while updating admin assignment, but the UI has been updated locally."
      );
    }
  };

  const handleMove = useCallback(
    async ({ dragIds, parentId }) => {
      // Find the node to move and the target parent
      const dragId = Array.isArray(dragIds) ? dragIds[0] : dragIds;

      try {
        // Call hierarchyService to move node
        const response = await hierarchyService.moveHierarchyNode(
          dragId,
          parentId
        );

        if (response) {
          // If successful, update client-side
          performClientSideMove(dragId, parentId);
        } else {
          // If fails, still update client
          performClientSideMove(dragId, parentId);

        }
      } catch (error) {

        // Fall back to client-side move
        performClientSideMove(dragId, parentId);
      }
    },
    [treeData]
  );

  // Client-side node move implementation
  const performClientSideMove = (dragId, parentId) => {
    // Create a map of all nodes for faster access
    const nodeMap = new Map();
    const buildMap = (nodes) => {
      nodes.forEach((node) => {
        if (node.id) nodeMap.set(node.id.toString(), node);
        if (node.children) buildMap(node.children);
      });
    };
    buildMap(treeData);

    // Find the node to move
    const nodeToMove = nodeMap.get(dragId.toString());
    if (!nodeToMove) return;

    // Remove from old parent
    const newTree = [...treeData];
    const removeFromParent = (nodes) => {
      return nodes.filter((node) => {
        if (node.id && node.id.toString() === nodeToMove.id.toString())
          return false;
        if (node.children) {
          node.children = removeFromParent(node.children);
        }
        return true;
      });
    };
    const cleanedTree = removeFromParent(newTree);

    // Add to new parent
    if (parentId) {
      const parentNode = nodeMap.get(parentId.toString());
      if (parentNode) {
        parentNode.children = [...(parentNode.children || []), nodeToMove];
      }
      setTreeData(cleanedTree);
    } else {
      setTreeData([...cleanedTree, nodeToMove]);
    }
  };

  // Get all nodes for use in modals and filtering
  const allNodes = flattenTree(treeData);

  // Helper function to check if a node is a root node
  const isNodeRoot = (nodeId) => {
    return treeData.some(rootNode => rootNode.id === nodeId);
  };

  // Filter exchange nodes based on search term and category
  const filterExchangeNodes = useCallback((searchTerm, categoryFilter) => {
    // Start with all nodes except the selected one and root nodes
    let filtered = allNodes.filter((n) => {
      const isSelected = n.id === selectedNodeId;
      const isRoot = isNodeRoot(n.id);
      return !isSelected && !isRoot;
    });

    // Filter by category if selected
    if (categoryFilter) {
      filtered = filtered.filter((node) => node.category === categoryFilter);
    }

    // Filter by search term
    if (searchTerm) {
      const searchTermLower = searchTerm.toLowerCase();
      filtered = filtered.filter((node) => {
        const nameMatch = node.name && node.name.toLowerCase().includes(searchTermLower);
        const codeMatch = node.code && node.code.toLowerCase().includes(searchTermLower);
        return nameMatch || codeMatch;
      });
    }

    setFilteredExchangeNodes(filtered);
  }, [allNodes, selectedNodeId, treeData]);

  // Update filtered exchange nodes when search term or category changes
  useEffect(() => {
    if (isExchangeModalOpen) {
      filterExchangeNodes(exchangeSearchTerm, selectedCategoryFilter);
    }
  }, [exchangeSearchTerm, selectedCategoryFilter, isExchangeModalOpen, filterExchangeNodes]);

  // Initialize filtered exchange nodes when modal opens
  useEffect(() => {
    if (isExchangeModalOpen) {
      // Reset search states and initialize with all available nodes
      setExchangeSearchTerm("");
      setSelectedCategoryFilter("");
      setExchangeNodeId(null);
      filterExchangeNodes("", "");
    }
  }, [isExchangeModalOpen, filterExchangeNodes]);

  const renderNode = ({ node, style, dragHandle }) => {
    // Extract node data with defaults for safety
    const {
      name = "Unnamed",
      leaf = "N",
      isLeaf = false,
      children = [],
      code = "",
      activeStatus = "Y",
      category = "",
    } = node.data || {};

    // Determine node properties
    const hasChildren = isLeaf === false || (children && children.length > 0);
    const typeName = getNodeType(category);
    const isActive = activeStatus === "Y";
    const isLeafNode = leaf === "Y";

    // Check if this is a root node (node is directly in treeData, not nested as a child)
    const isRootNode = treeData.some(rootNode => rootNode.id === node.id);

    // User assignment status
    const assignedUsers = nodesWithAssignedUsers[node.id] || [];
    const hasAssignedUsers = assignedUsers.length > 0;

    // Visual state classes
    const badgeClass = isActive
      ? "bg-blue-100 text-blue-700"
      : "bg-gray-100 text-gray-700";
    const isHighlighted = highlightedNodeIds.includes(node.id);
    const highlightClass = isHighlighted
      ? "bg-yellow-100 border-l-4 border-yellow-500"
      : "";
    const isLoading = loadingNodes[node.id];

    // Handle toggle with loading only immediate children
    const handleToggle = (e) => {
      try {
        if (e) e.stopPropagation();

        // Safety check
        if (!node?.id) return;

        const nodeId = node.id.toString();
        const isNodeOpen = node.isOpen;

        console.log(
          `Toggle node ${nodeId}: ${isNodeOpen ? "closing" : "opening"}`
        );

        // Toggle node state
        if (isNodeOpen) {
          // Close node
          node.close();

          // Track manually collapsed nodes when in expanded mode
          if (allExpanded) {
            setManuallyCollapsedNodes((prev) => {
              const newSet = new Set(prev);
              newSet.add(nodeId);
              return newSet;
            });
          }
        } else {
          // Open node
          // Remove from manually collapsed set
          setManuallyCollapsedNodes((prev) => {
            const newSet = new Set(prev);
            newSet.delete(nodeId);
            return newSet;
          });

          // Load children if needed
          if (
            typeof hasLoadedChildren === "function" &&
            !hasLoadedChildren(nodeId)
          ) {
            const nodePath = findNodePath(treeData, nodeId);
            loadChildrenForNode(nodeId, false, nodePath);
          }

          node.open();
        }
      } catch (error) {

      }
    };

    return (
      <div
        style={style}
        className={`flex items-center space-x-2 ${highlightClass} p-1 ${
          isHighlighted ? "pl-2" : "pl-4"
        } ${!isActive ? "opacity-60" : ""}`}
      >
        {/* Toggle button or spacer */}
        {hasChildren ? (
          <button
            onClick={handleToggle}
            className="p-1 hover:bg-gray-100 rounded-full transition-colors mr-1"
            disabled={isLoading}
            title={
              manuallyCollapsedNodes.has(node.id.toString()) && allExpanded
                ? "This node was manually collapsed (click to expand)"
                : "Toggle expand/collapse"
            }
          >
            {isLoading ? (
              <div className="w-4 h-4 border-2 border-t-transparent border-blue-500 rounded-full animate-spin" />
            ) : node.isOpen ? (
              <ChevronDown className="w-4 h-4 text-gray-500" />
            ) : (
              <ChevronRight
                className={`w-4 h-4 ${
                  manuallyCollapsedNodes.has(node.id.toString()) && allExpanded
                    ? "text-red-500"
                    : "text-gray-500"
                }`}
              />
            )}
          </button>
        ) : (
          <div className="w-6 h-6 mr-1" />
        )}

        {/* Node icon based on category and leaf status */}
        <NodeIcon
          isLeaf={leaf}
          category={category}
          onClick={hasChildren ? handleToggle : undefined}
        />

        <div
          ref={dragHandle}
          className="flex items-center cursor-grab active:cursor-grabbing"
        >
          {/* Node name and code */}
          <span
            className="text-md font-normal mr-2 cursor-pointer hover:text-blue-600"
            style={{
              fontFamily:
                '"Lato", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
            }}
            onClick={(e) => {
              e.stopPropagation();

              // Safety check
              if (!node?.id) return;

              try {
                // Use the appropriate action based on node type
                if (hasChildren) {
                  handleToggle(e);
                } else if (isLeafNode) {
                  // Try handleClick first, fall back to select
                  if (typeof node.handleClick === "function") {
                    node.handleClick(e);
                  } else if (typeof node.select === "function") {
                    node.select();
                  }
                }
              } catch (error) {

              }
            }}
            title={
              hasChildren
                ? "Click to toggle expand/collapse"
                : "Click to select"
            }
          >
            {name}{" "}
            {code && <span className="text-xs text-gray-500">({code})</span>}
          </span>

          {/* Add node button */}
          <button
            onClick={async (e) => {
              e.stopPropagation();

              setSelectedNodeId(node.id);

              // Fetch parent node details (includes posting details automatically)
              setLoadingParentDetails(true);
              setPostingDetailsLoaded(false);
              setPostingDetailsError(null);

              try {
                const parentDetails = await hierarchyService.fetchHierarchyData(node.id, 'N');
                // Successfully fetched parent details with posting details
                console.log("Parent details response:", JSON.stringify(parentDetails, null, 2));

                if (parentDetails) {
                  console.log("Full parent details:", parentDetails);

                  // Check if postingDetails exists and is an array
                  if (parentDetails.postingDetails && Array.isArray(parentDetails.postingDetails)) {
                    console.log("Posting details array:", parentDetails.postingDetails);

                    // Validate each posting detail to ensure it has required fields
                    const validPostings = parentDetails.postingDetails.filter(post => {
                      // Each posting must have either postingId or id, and either postingName or name
                      return (post.postingId || post.id) && (post.postingName || post.name);
                    });

                    if (validPostings.length > 0) {
                      // Replace with only valid postings
                      parentDetails.postingDetails = validPostings;
                      setFilteredPostings(validPostings); // Initialize filtered postings
                      setPostingDetailsLoaded(true);
                    } else {
                      console.log("No valid posting details found in the array");
                      parentDetails.postingDetails = [];
                      setFilteredPostings([]);
                      setPostingDetailsLoaded(true); // Still mark as loaded, but array will be empty
                    }
                  }
                  // Check if postingDetails exists but is not an array (might be a single object)
                  else if (parentDetails.postingDetails && typeof parentDetails.postingDetails === 'object') {
                    console.log("Posting details object:", parentDetails.postingDetails);

                    const post = parentDetails.postingDetails;
                    // Validate the posting object
                    if ((post.postingId || post.id) && (post.postingName || post.name)) {
                      // Convert to array if it's a single valid object
                      parentDetails.postingDetails = [parentDetails.postingDetails];
                      setFilteredPostings(parentDetails.postingDetails);
                      setPostingDetailsLoaded(true);
                    } else {
                      console.log("Invalid posting details object");
                      parentDetails.postingDetails = []; // Empty array
                      setFilteredPostings([]);
                      setPostingDetailsLoaded(true);
                    }
                  }
                  // Check if there's a different property that might contain posting details
                  else if (parentDetails.postings && Array.isArray(parentDetails.postings)) {
                    console.log("Found postings property:", parentDetails.postings);

                    // Validate each posting
                    const validPostings = parentDetails.postings.filter(post => {
                      return (post.postingId || post.id) && (post.postingName || post.name);
                    });

                    if (validPostings.length > 0) {
                      parentDetails.postingDetails = validPostings;
                      setFilteredPostings(validPostings);
                      setPostingDetailsLoaded(true);
                    } else {
                      console.log("No valid posting details found in postings array");
                      parentDetails.postingDetails = []; // Empty array
                      setFilteredPostings([]);
                      setPostingDetailsLoaded(true);
                    }
                  }
                  else {
                    console.log("No posting details found in the response");
                    parentDetails.postingDetails = []; // Ensure it's an empty array
                    setFilteredPostings([]);
                    setPostingDetailsLoaded(true);
                  }
                } else {
                  console.log("No parent details found in the response");
                  setPostingDetailsLoaded(false);
                }

                setParentNodeDetails(parentDetails);
              } catch (error) {
                // If there's an error, we'll still use the local node data
                console.error("Error fetching parent details:", error);
                setParentNodeDetails(null);
                setPostingDetailsError(error.toString());
                alert("Error fetching parent details: " + error);
              } finally {
                setLoadingParentDetails(false);
                setIsAddModalOpen(true);
              }
            }}
            className="mr-2 hover:bg-green-50 p-1 rounded-full"
            disabled={!isActive}
          >
            <PlusCircle className="w-5 h-5 text-green-500" />
          </button>

          {/* Load all children button - only for nodes with leaf !== 'Y' */}
          {leaf !== "Y" && (
            <button
              onClick={(e) => {
                e.stopPropagation();

                // Call loadChildrenForNode with autoLoadNonLeafChildren=true
                // Get the path to this node to use as ancestors
                const nodePath = findNodePath(treeData, node.id);
                loadChildrenForNode(node.id, true, nodePath);
              }}
              className="mr-2 hover:bg-blue-50 p-1 rounded-full"
              disabled={!isActive}
              title="Load all children recursively"
            >
              <FolderOpen className="w-5 h-5 text-blue-500" />
            </button>
          )}

          {/* Exchange node button - disabled for root nodes */}
          {!isRootNode && (
            <button
              onClick={(e) => {
                e.stopPropagation();

                setSelectedNodeId(node.id);
                setIsExchangeModalOpen(true);
              }}
              className="mr-2 hover:bg-orange-50 p-1 rounded-full"
              disabled={!isActive}
              title="Exchange position with another node"
            >
              <ArrowLeftRight className="w-5 h-5 text-yellow-500" />
            </button>
          )}

          {/* User assignment button - for leaf nodes and seat nodes */}
          {isLeafNode && (
            <button
              onClick={(e) => {
                e.stopPropagation();

                openUserAssignModal(node.id);
              }}
              className={`mr-2 p-1.5 rounded-md ${
                hasAssignedUsers
                  ? "bg-green-600 hover:bg-green-700 text-white"
                  : "bg-gray-100 hover:bg-gray-200 text-gray-700"
              }`}
              disabled={!isActive}
              title={
                hasAssignedUsers
                  ? "View or change assigned user"
                  : "Assign a user to this leaf node"
              }
            >
              <div className="flex items-center">
                <User className="w-4 h-4 mr-1" />
                <span className="text-xs font-medium">
                  {hasAssignedUsers ? "Assigned" : "Not Assigned"}
                </span>
              </div>
            </button>
          )}

          {/* Admin assignment button - for non-seat nodes */}
          {category !== "S" && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                openAdminAssignModal(node.id);
              }}
              className={`mr-2 p-1.5 rounded-md ${
                nodesWithAssignedAdmins[node.id] && nodesWithAssignedAdmins[node.id].length > 0
                  ? "bg-purple-600 hover:bg-purple-700 text-white"
                  : "bg-gray-100 hover:bg-gray-200 text-gray-700"
              }`}
              disabled={!isActive}
              title={
                nodesWithAssignedAdmins[node.id] && nodesWithAssignedAdmins[node.id].length > 0
                  ? "View or change assigned admin"
                  : "Assign an admin to this node"
              }
            >
              <div className="flex items-center">
                <Users className="w-4 h-4 mr-1" />
                <span className="text-xs font-medium">
                  {nodesWithAssignedAdmins[node.id] && nodesWithAssignedAdmins[node.id].length > 0
                    ? "Admin"
                    : "No Admin"}
                </span>
              </div>
            </button>
          )}

          {/* Node type badge */}
          <span
            className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${badgeClass}`}
          >
            {typeName}
          </span>
        </div>
      </div>
    );
  };

  useEffect(() => {
    if (treeInstance && treeReady) {


      // Only initialize the expansion state, but don't automatically close all nodes
      // This allows the tree to maintain its current state when re-rendered
      if (!allExpanded) {

      } else {

      }
    }
  }, [treeInstance, treeReady, allExpanded]);

  // Render loading state
  if (isLoading) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-6 flex justify-center items-center h-96">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-t-transparent border-blue-500 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading hierarchy data...</p>
        </div>
      </div>
    );
  }

  // Render error state
  if (error && treeData.length === 0) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="bg-red-50 border border-red-200 rounded-md p-4 flex items-start">
          <AlertCircle className="w-5 h-5 text-red-500 mr-3 mt-0.5" />
          <div>
            <h3 className="text-red-800 font-medium">
              Error loading hierarchy
            </h3>
            <p className="text-red-700 mt-1">{error}</p>
            <button
              onClick={fetchRootHierarchy}
              className="mt-3 px-4 py-2 bg-red-100 text-red-800 rounded-md hover:bg-red-200"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen ">
      <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <h2 className="text-xl font-semibold mr-4">Hierarchy Setup</h2>
            <button
              onClick={toggleExpandAll}
              disabled={!treeReady || isExpandingAll}
              className={`flex items-center px-3 py-1 rounded-md transition-colors ${
                treeReady && !isExpandingAll
                  ? "bg-gray-100 hover:bg-gray-200 text-gray-700"
                  : "bg-gray-50 text-gray-400 cursor-not-allowed"
              }`}
            >
              {isExpandingAll ? (
                <>
                  <div className="w-4 h-4 border-2 border-t-transparent border-blue-500 rounded-full animate-spin mr-2" />
                  <span>Processing...</span>
                </>
              ) : allExpanded ? (
                <>
                  <ChevronsUp className="w-4 h-4 mr-1" />
                  <span>Collapse All</span>
                </>
              ) : (
                <>
                  <ChevronsDown className="w-4 h-4 mr-1" />
                  <span>Expand All</span>
                </>
              )}
            </button>
            <button
              onClick={fetchRootHierarchy}
              className="ml-2 flex items-center px-3 py-1 rounded-md bg-blue-50 hover:bg-blue-100 text-blue-700"
            >
              <RefreshCw className="w-4 h-4 mr-1" />
              <span>Refresh</span>
            </button>
          </div>
          <div className="relative w-64">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search hierarchy..."
              value={globalSearchTerm}
              onChange={(e) => setGlobalSearchTerm(e.target.value)}
              className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md"
            />
            {globalSearchTerm && (
              <button
                onClick={() => setGlobalSearchTerm("")}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <X className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>

        {globalSearchTerm && highlightedNodeIds.length > 0 && (
          <div className="mb-4 text-sm text-gray-600">
            Found {highlightedNodeIds.length} matching{" "}
            {highlightedNodeIds.length === 1 ? "node" : "nodes"}
          </div>
        )}

        {globalSearchTerm && highlightedNodeIds.length === 0 && (
          <div className="mb-4 p-3 bg-yellow-50 text-yellow-700 border border-yellow-200 rounded-md">
            No matching nodes found for "{globalSearchTerm}"
          </div>
        )}

        {treeData.length === 0 ? (
          <div className="p-8 text-center border-2 border-dashed border-gray-300 rounded-lg">
            <Users className="w-12 h-12 text-gray-400 mx-auto mb-3" />
            <h3 className="text-lg font-medium text-gray-600 mb-2">
              No hierarchy data found
            </h3>
            <p className="text-gray-500 mb-4">
              The hierarchy appears to be empty or data could not be loaded.
            </p>
            <button
              onClick={fetchRootHierarchy}
              className="px-4 py-2 bg-blue-50 text-blue-700 rounded-md hover:bg-blue-100"
            >
              <RefreshCw className="w-4 h-4 mr-1 inline-block" />
              <span>Refresh Data</span>
            </button>
          </div>
        ) : (
          <div
            className="relative w-full h-full"
            // No action needed when clicking outside any tree node
          >
            <Tree
              ref={(instance) => {
                if (instance) {

                  // Always update the tree instance to ensure we have the latest reference
                  setTreeInstance(instance);
                  setTreeReady(true);


                  // Ensure the tree is in the correct state
                  if (allExpanded && typeof instance.openAll === "function") {
                    // Immediately open all nodes when allExpanded is true
                    try {
                      instance.openAll();

                    } catch (e) {

                    }
                  }
                }
              }}
              data={treeData}
              onMove={handleMove}
              rowHeight={36}
              openByDefault={allExpanded}
              width="100%"
              height={700} // Using a larger fixed height value
              indent={24}
              overscanCount={10}
              onClick={(node) => {
                // Safety check for valid node
                if (!node?.id) return;

                try {
                  // This handler is for row background clicks
                  // Handle row background clicks

                  // For non-leaf nodes, toggle expansion
                  if (!node.isLeaf) {
                    // Find the node in the rendered nodes and use its handleToggle
                    const renderedNode = treeInstance?.get(node.id);
                    if (renderedNode) {
                      // Use the same toggle logic as the node's toggle button
                      const nodeData = renderedNode.data;
                      const hasChildren =
                        !nodeData.isLeaf ||
                        (nodeData.children && nodeData.children.length > 0);

                      if (hasChildren) {
                        // Toggle the node using the same logic as the toggle button
                        if (node.isOpen) {
                          node.close();
                          if (allExpanded) {
                            setManuallyCollapsedNodes((prev) => {
                              const newSet = new Set(prev);
                              newSet.add(node.id.toString());
                              return newSet;
                            });
                          }
                        } else {
                          node.open();
                          setManuallyCollapsedNodes((prev) => {
                            const newSet = new Set(prev);
                            newSet.delete(node.id.toString());
                            return newSet;
                          });

                          // Load children if needed
                          if (
                            typeof hasLoadedChildren === "function" &&
                            !hasLoadedChildren(node.id)
                          ) {
                            const nodePath = findNodePath(treeData, node.id);
                            loadChildrenForNode(node.id, false, nodePath);
                          }
                        }
                      }
                    }
                  } else {
                    // For leaf nodes, select and activate
                    if (typeof node.select === "function") node.select();
                    if (typeof node.activate === "function") node.activate();
                  }
                } catch (error) {

                }
              }}
            >
              {renderNode}
            </Tree>
          </div>
        )}

        {/* Add Node Modal */}
        <Modal
          isOpen={isAddModalOpen}
          onClose={() => {
            setIsAddModalOpen(false);
            setParentNodeDetails(null);
            setPostingDetailsLoaded(false);
            setPostingDetailsError(null);
            setSelectedPost(null);
            setPostSearchTerm("");
            setFilteredPostings([]);
          }}
          title="Add New Node"
          onOk={handleAddNode}
        >
          <div className="space-y-4">
            {/* Parent Node Details */}
            {selectedNodeId && (
              <div className="mb-4 p-3 bg-blue-50 rounded-md border border-blue-100">
                <div className="text-sm font-medium text-blue-800 mb-2">
                  Current Hierarchy Details
                </div>
                {loadingParentDetails ? (
                  <div className="flex justify-center py-3">
                    <div className="w-6 h-6 border-2 border-t-transparent border-blue-500 rounded-full animate-spin"></div>
                  </div>
                ) : (
                  <div>
                    <div className="flex items-center">
                      {(() => {
                        // Use parentNodeDetails from API if available, otherwise use local data
                        const allNodes = flattenTree(treeData);
                        const parentNode = parentNodeDetails || allNodes.find(
                          (n) => n.id === selectedNodeId
                        );

                        if (!parentNode) return null;

                        // Handle different property names from API vs local data
                        const nodeName = parentNode.hierarchyName || parentNode.name;
                        const nodeCode = parentNode.hierarchyCode || parentNode.code;
                        const nodeCategory = parentNode.category;
                        const nodeLeaf = parentNode.leaf;

                        return (
                          <>
                            <div className="bg-blue-100 p-2 rounded-full mr-3">
                              <NodeIcon
                                isLeaf={nodeLeaf}
                                category={nodeCategory}
                                onClick={null}
                              />
                            </div>
                            <div>
                              <div className="font-medium text-blue-700">
                                {nodeName}
                              </div>
                              <div className="text-xs text-blue-600">
                                {nodeCode && (
                                  <span> Hierarchy Code: {nodeCode}</span>
                                )}
                                <span className="ml-2">
                                  Category: {getNodeType(nodeCategory)}
                                </span>
                              </div>
                            </div>
                          </>
                        );
                      })()}
                    </div>

                    {/* Display posting details if available */}
                    {/* {parentNodeDetails && parentNodeDetails.postingDetails && parentNodeDetails.postingDetails.length > 0 && (
                      <div className="mt-3 pt-3 border-t border-blue-200">
                        <div className="text-sm font-medium text-blue-800 mb-2">
                          Available Postings
                        </div>
                        {parentNodeDetails.postingDetails.map((posting, index) => (
                          <div key={index} className={index > 0 ? "mt-3 pt-3 border-t border-blue-100" : ""}>
                            {index > 0 && <div className="text-xs text-blue-600 mb-2">Posting {index + 1}</div>}
                            <div className="grid grid-cols-2 gap-2 text-sm">
                              <div className="text-blue-600 font-medium">Post Name:</div>
                              <div className="text-blue-700">{posting.postingName || 'N/A'}</div>

                              <div className="text-blue-600 font-medium">Post ID:</div>
                              <div className="text-blue-700">{posting.postingId || 'N/A'}</div>

                              {posting.postType && (
                                <>
                                  <div className="text-blue-600 font-medium">Post Type:</div>
                                  <div className="text-blue-700">{posting.postType}</div>
                                </>
                              )}

                              {posting.department && (
                                <>
                                  <div className="text-blue-600 font-medium">Department:</div>
                                  <div className="text-blue-700">{posting.department}</div>
                                </>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    )} */}

                    {/* Display office types if available */}
                    {/* {parentNodeDetails && parentNodeDetails.officeTypes && parentNodeDetails.officeTypes.length > 0 && (
                      <div className="mt-3 pt-3 border-t border-blue-200">
                        <div className="text-sm font-medium text-blue-800 mb-2">
                          Office Types
                        </div>
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          {parentNodeDetails.officeTypes.map((officeType, index) => (
                            <Fragment key={index}>
                              <div className="text-blue-600 font-medium">Type {index + 1}:</div>
                              <div className="text-blue-700">{officeType.name || officeType.type || 'N/A'}</div>
                            </Fragment>
                          ))}
                        </div>
                      </div>
                    )} */}

                    {/* Display parent hierarchy name if available */}
                    {parentNodeDetails && parentNodeDetails.parentHierarchyName && (
                      <div className="mt-3 pt-3 border-t border-blue-200">
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <div className="text-blue-600 font-medium">Parent Hierarchy:</div>
                          <div className="text-blue-700">{parentNodeDetails.parentHierarchyName}</div>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Hierarchy Name
              </label>
              <input
                type="text"
                value={newNodeName}
                onChange={(e) => setNewNodeName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                placeholder="Enter Hierarchy name"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Hierarchy Code
              </label>
              <input
                type="text"
                value={newNodeCode}
                onChange={(e) => setNewNodeCode(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                placeholder="Enter Hierarchy code"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Category
              </label>
              <select
                value={newNodeType}
                onChange={(e) => {
                  setNewNodeType(e.target.value);
                  // Reset post selection when changing category
                  if (e.target.value === "S") {
                    // Load posts when Seat is selected
                    fetchPosts("");
                    setPostSearchTerm("");

                    // Reset filtered postings to show all available postings
                    if (parentNodeDetails && parentNodeDetails.postingDetails) {
                      setFilteredPostings(parentNodeDetails.postingDetails);
                    }
                  } else {
                    setSelectedPost(null);
                    setPostSearchTerm("");
                    setFilteredPostings([]);
                  }
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="O">Office</option>
                <option value="D">Department</option>
                <option value="R">Directorate</option>
                <option value="T">Treasury</option>
                <option value="S">Seat</option>
                <option value="G">Finance</option>
              </select>
            </div>

            {/* Post autocomplete field - only shown for Seat category */}
            {newNodeType === "S" && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Post <span className="text-red-500">*</span>
                </label>

                {/* Use posting details from parent node if available */}
                {loadingParentDetails ? (
                  <div className="flex justify-center py-3 mb-4">
                    <div className="w-6 h-6 border-2 border-t-transparent border-blue-500 rounded-full animate-spin"></div>
                    <span className="ml-2 text-blue-600">Loading posting details...</span>
                  </div>
                ) : postingDetailsError ? (
                  <div className="mb-4 p-3 bg-red-50 text-red-700 border border-red-200 rounded-md">
                    <p>Error loading posting details: {postingDetailsError}</p>
                    <p className="text-sm mt-1">Please try again or search for a post below.</p>
                  </div>
                ) : parentNodeDetails && parentNodeDetails.postingDetails && Array.isArray(parentNodeDetails.postingDetails) && parentNodeDetails.postingDetails.length > 0 ? (
                  <div>
                    <div className="text-sm text-gray-600 mb-2">
                      Select from available posting details:
                    </div>

                    {/* Search input for filtering postings */}
                    <div className="relative mb-3">
                      <input
                        type="text"
                        placeholder="Search for a post..."
                        value={postSearchTerm}
                        onChange={(e) => {
                          const searchTerm = e.target.value.toLowerCase();
                          setPostSearchTerm(searchTerm);

                          // Filter the postings based on the search term
                          if (searchTerm.trim() === '') {
                            setFilteredPostings(parentNodeDetails.postingDetails);
                          } else {
                            const filtered = parentNodeDetails.postingDetails.filter(post => {
                              const postName = (post.postingName || post.name || '').toLowerCase();
                              const postId = (post.postingId || post.id || '').toString().toLowerCase();
                              const postType = (post.postType || post.type || '').toLowerCase();
                              const dept = (post.department || '').toLowerCase();

                              return postName.includes(searchTerm) ||
                                     postId.includes(searchTerm) ||
                                     postType.includes(searchTerm) ||
                                     dept.includes(searchTerm);
                            });
                            setFilteredPostings(filtered);
                          }
                        }}
                        className="w-full pl-9 pr-3 py-2 border border-gray-300 rounded-md"
                      />
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      {postSearchTerm && (
                        <button
                          onClick={() => {
                            setPostSearchTerm("");
                            setFilteredPostings(parentNodeDetails.postingDetails);
                          }}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      )}
                    </div>

                    {/* Display filtered or all postings */}
                    <div className="max-h-40 overflow-y-auto border border-gray-300 rounded-md">
                      {(postSearchTerm ? filteredPostings : parentNodeDetails.postingDetails).map((post, index) => {
                        return (
                          <div
                            key={index}
                            className={`flex items-center p-2 hover:bg-gray-50 border-b border-gray-200 ${
                              selectedPost &&
                              (selectedPost === post ||
                               (post.postingId && selectedPost.postingId === post.postingId))
                                ? "bg-blue-50"
                                : ""
                            }`}
                          >
                            <input
                              type="radio"
                              id={`parent-post-${index}`}
                              name="selectedPost"
                              checked={
                                selectedPost &&
                                (selectedPost === post ||
                                 (post.postingId && selectedPost.postingId === post.postingId))
                              }
                              onChange={() => {
                                console.log("Selected post from parent details:", post);
                                // Use the actual posting object directly
                                setSelectedPost(post);
                              }}
                              className="h-4 w-4 text-blue-600 border-gray-300"
                            />
                            <label
                              htmlFor={`parent-post-${index}`}
                              className="ml-2 block text-sm text-gray-700 cursor-pointer flex-1"
                            >
                              <div className="font-medium">{post.postingName || post.name || 'Unknown'}</div>
                              <div className="text-xs text-gray-500">ID: {post.postingId || post.id || 'N/A'}</div>
                              {(post.postType || post.type) && (
                                <div className="text-xs text-gray-500">
                                  Type: {post.postType || post.type}
                                </div>
                              )}
                              {post.department && (
                                <div className="text-xs text-gray-500">
                                  Dept: {post.department}
                                </div>
                              )}
                            </label>
                          </div>
                        );
                      })}

                      {/* Show message when no results found */}
                      {postSearchTerm && filteredPostings.length === 0 && (
                        <div className="p-3 text-center text-gray-500">
                          No matching posts found
                        </div>
                      )}
                    </div>
                  </div>
                ) : postingDetailsLoaded ? (
                  <div className="mb-4 p-3 bg-yellow-50 text-yellow-700 border border-yellow-200 rounded-md">
                    <p>No posting details available for this parent node.</p>
                    <p className="text-sm mt-1">Please select a different parent node with posting details.</p>
                  </div>
                ) : (
                  <div className="mb-2 text-sm text-gray-600">
                    Waiting for posting details...
                  </div>
                )}

                {/* No regular post search - only show parent node's posting details */}

                {/* Selected post display */}
                {selectedPost && (
                  <div className="mt-3 p-3 bg-blue-50 rounded-md border border-blue-100">
                    <div className="text-sm font-medium text-blue-700 mb-2">
                      Selected Post
                    </div>
                    <div className="flex items-center bg-white text-blue-800 px-3 py-2 rounded-md border border-blue-200">
                      <div className="flex-1">
                        {/* Log the selected post for debugging */}
                        {console.log("Displaying selected post:", selectedPost)}

                        <div className="font-medium">
                          {selectedPost.postingName || selectedPost.name || 'Unknown'}
                        </div>
                        <div className="text-xs text-gray-600">
                          ID: {selectedPost.postingId || selectedPost.id || 'N/A'}
                        </div>
                        {(selectedPost.postType || selectedPost.type) && (
                          <div className="text-xs text-gray-600">
                            Type: {selectedPost.postType || selectedPost.type}
                          </div>
                        )}
                        {selectedPost.department && (
                          <div className="text-xs text-gray-600">
                            Department: {selectedPost.department}
                          </div>
                        )}
                      </div>
                      <button
                        onClick={() => {
                          console.log("Clearing selected post");
                          setSelectedPost(null);
                        }}
                        className="ml-2 text-blue-600 hover:text-blue-800 p-1 hover:bg-blue-50 rounded-full"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </Modal>

        {/* Exchange Node Modal */}
        <Modal
          isOpen={isExchangeModalOpen}
          onClose={() => {
            setIsExchangeModalOpen(false);
            setExchangeSearchTerm("");
            setSelectedCategoryFilter("");
            setExchangeNodeId(null);
            setFilteredExchangeNodes([]);
          }}
          title="Move Hierarchy"
          onOk={handleExchangeNodes}
        >
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              Select another node to exchange positions with the selected node.
            </p>
            <p className="text-xs text-orange-600 bg-orange-50 p-2 rounded-md border border-orange-200">
              <strong>Note:</strong> Root nodes cannot be exchanged and are not shown in the selection list.
            </p>

            {/* Selected Node Details */}
            {selectedNodeId && (
              <div className="mb-4 p-3 bg-blue-50 rounded-md border border-blue-100">
                <div className="text-sm font-medium text-blue-800 mb-2">
                  Selected Node Details
                </div>
                <div className="flex items-center">
                  {(() => {
                    const node = allNodes.find((n) => n.id === selectedNodeId);
                    if (!node) return null;

                    return (
                      <>
                        <div className="bg-blue-100 p-2 rounded-full mr-3">
                          <NodeIcon
                            isLeaf={node.leaf}
                            category={node.category}
                            onClick={null}
                          />
                        </div>
                        <div>
                          <div className="font-medium text-blue-700">
                            {node.name}
                          </div>
                          <div className="text-xs text-blue-600">
                            {node.code && <span>Code: {node.code}</span>}
                            <span className="ml-2">
                              Type: {getNodeType(node.category)}
                            </span>
                          </div>
                        </div>
                      </>
                    );
                  })()}
                </div>
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Node to Exchange With
              </label>

              {/* Category Filter */}
              <div className="mb-3">
                <label className="block text-xs font-medium text-gray-600 mb-1">
                  Filter by Category
                </label>
                <select
                  value={selectedCategoryFilter}
                  onChange={(e) => setSelectedCategoryFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                >
                  <option value="">All Categories</option>
                  <option value="O">Office</option>
                  <option value="D">Department</option>
                  <option value="R">Directorate</option>
                  <option value="T">Treasury</option>
                  <option value="S">Seat</option>
                  <option value="G">Finance</option>
                </select>
              </div>

              {/* Search Input */}
              <div className="relative mb-3">
                <input
                  type="text"
                  placeholder="Search nodes by name or code..."
                  value={exchangeSearchTerm}
                  onChange={(e) => setExchangeSearchTerm(e.target.value)}
                  className="w-full pl-9 pr-3 py-2 border border-gray-300 rounded-md"
                />
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                {exchangeSearchTerm && (
                  <button
                    onClick={() => setExchangeSearchTerm("")}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    <X className="w-4 h-4" />
                  </button>
                )}
              </div>

              {/* Node Selection List */}
              {filteredExchangeNodes.length === 0 ? (
                <div className="text-center py-4 text-gray-500 border border-gray-300 rounded-md">
                  {exchangeSearchTerm || selectedCategoryFilter ? "No matching nodes found" : "No exchangeable nodes available"}
                </div>
              ) : (
                <div className="max-h-60 overflow-y-auto border border-gray-300 rounded-md">
                  {filteredExchangeNodes.map((node) => (
                    <div
                      key={node.id}
                      className={`flex items-center p-3 hover:bg-gray-50 border-b border-gray-200 cursor-pointer ${
                        exchangeNodeId === node.id ? "bg-yellow-50 border-yellow-200" : ""
                      }`}
                      onClick={() => setExchangeNodeId(node.id)}
                    >
                      <input
                        type="radio"
                        id={`exchange-node-${node.id}`}
                        name="exchangeNode"
                        checked={exchangeNodeId === node.id}
                        onChange={() => setExchangeNodeId(node.id)}
                        className="h-4 w-4 text-yellow-600 border-gray-300 mr-3"
                      />
                      <div className="flex items-center flex-1">
                        <div className="bg-gray-100 p-2 rounded-full mr-3">
                          <NodeIcon
                            isLeaf={node.leaf}
                            category={node.category}
                            onClick={null}
                          />
                        </div>
                        <div className="flex-1">
                          <div className="font-medium text-gray-900">
                            {node.name}
                          </div>
                          <div className="text-xs text-gray-500">
                            {node.code && <span>Code: {node.code}</span>}
                            <span className={node.code ? "ml-2" : ""}>
                              Type: {getNodeType(node.category)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Exchange Node Details */}
            {exchangeNodeId && (
              <div className="mt-4 p-3 bg-yellow-50 rounded-md border border-yellow-100">
                <div className="text-sm font-medium text-yellow-800 mb-2">
                  Exchange With Node
                </div>
                <div className="flex items-center">
                  {(() => {
                    const node = allNodes.find((n) => n.id === exchangeNodeId);
                    if (!node) return null;

                    return (
                      <>
                        <div className="bg-yellow-100 p-2 rounded-full mr-3">
                          <NodeIcon
                            isLeaf={node.leaf}
                            category={node.category}
                            onClick={null}
                          />
                        </div>
                        <div>
                          <div className="font-medium text-yellow-700">
                            {node.name}
                          </div>
                          <div className="text-xs text-yellow-600">
                            {node.code && <span>Code: {node.code}</span>}
                            <span className="ml-2">
                              Type: {getNodeType(node.category)}
                            </span>
                          </div>
                        </div>
                      </>
                    );
                  })()}
                </div>
              </div>
            )}
          </div>
        </Modal>

        {/* User Assignment Modal */}
        <Modal
          isOpen={isUserAssignModalOpen}
          onClose={() => setIsUserAssignModalOpen(false)}
          title="Assign User to Seat"
          onOk={handleAssignUsers}
          // Custom footer with unassign button
          footer={
            <div className="flex justify-end space-x-2">
              {/* Show only Unassign button when a user is assigned */}
              {nodesWithAssignedUsers[selectedNodeId] &&
                nodesWithAssignedUsers[selectedNodeId].length > 0 ? (
                  <>
                    <button
                      onClick={() => setIsUserAssignModalOpen(false)}
                      className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-md"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={async () => {
                        try {
                          // Prepare for unassignment
                          // The hierarchyId is passed directly to the unassignUserFromNode function



                          // Call the hierarchyService method
                          const response = await hierarchyService.unassignUserFromNode(selectedNodeId);



                          if (response) {
                            // Update the local state to reflect unassigned user
                            setNodesWithAssignedUsers((prev) => ({
                              ...prev,
                              [selectedNodeId]: [],
                            }));
                            setSelectedUsers([]);

                            // Close the modal
                            setIsUserAssignModalOpen(false);
                          } else {
                            console.error("Failed to unassign user");
                            alert("Failed to unassign user. Please try again.");
                          }
                        } catch (error) {
                          console.error("Error unassigning user:", error);

                          // Even if there's an error, update the UI for better user experience
                          setNodesWithAssignedUsers((prev) => ({
                            ...prev,
                            [selectedNodeId]: [],
                          }));
                          setSelectedUsers([]);

                          // Close the modal
                          setIsUserAssignModalOpen(false);

                          alert("An error occurred while unassigning user, but the UI has been updated locally.");
                        }
                      }}
                      className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600"
                    >
                      Unassign User
                    </button>
                  </>
                ) : (
                  <>
                    <button
                      onClick={() => setIsUserAssignModalOpen(false)}
                      className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-md"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleAssignUsers}
                      className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                      disabled={selectedUsers.length === 0}
                    >
                      Assign User
                    </button>
                  </>
                )}
            </div>
          }
        >
          <div className="space-y-4">
            {selectedNode && (
              <div className="bg-blue-50 p-3 rounded-md mb-4">
                <h3 className="font-medium text-blue-800">Hierarchy Details</h3>
                <div className="mt-2 text-sm text-blue-700">
                  <p>
                    <span className="font-medium">Hierarchy Name:</span>{" "}
                    {selectedNode.name}
                  </p>
                  <p>
                    <span className="font-medium">Hierarchy Code:</span>{" "}
                    {selectedNode.code || "N/A"}
                  </p>
                  <p>
                    <span className="font-medium">Category:</span>{" "}
                    {getNodeType(selectedNode.category)}
                  </p>
                </div>
              </div>
            )}

            <div>
              {/* Current assignment status */}
              {nodesWithAssignedUsers[selectedNodeId] &&
              nodesWithAssignedUsers[selectedNodeId].length > 0 ? (
                <div className="mb-4 p-3 bg-green-50 rounded-md border border-green-100">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="bg-green-100 p-2 rounded-full mr-3">
                        <User className="w-5 h-5 text-green-600" />
                      </div>
                      <div>
                        <div className="text-sm font-medium text-green-800">
                          Currently Assigned User
                        </div>
                        <div className="font-medium text-green-700">
                          {nodesWithAssignedUsers[selectedNodeId][0].name}
                        </div>

                      </div>
                    </div>

                  </div>
                </div>
              ) : (
                <div className="mb-4 p-3 bg-gray-50 rounded-md border border-gray-200">
                  <div className="flex items-center">
                    <div className="bg-gray-100 p-2 rounded-full mr-3">
                      <User className="w-5 h-5 text-gray-500" />
                    </div>
                    <div>
                      <div className="text-sm font-medium text-gray-700">
                        No User Assigned
                      </div>
                      <div className="text-xs text-gray-500">
                        Select a user below to assign to this hierarchy
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Only show user search and selection when no user is assigned */}
              {!(nodesWithAssignedUsers[selectedNodeId] && nodesWithAssignedUsers[selectedNodeId].length > 0) && (
                <>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Select User
                  </label>

                  {/* Search input with autocomplete */}
                  <div className="relative mb-3">
                    <input
                      type="text"
                      placeholder="Search users by username..."
                      value={userSearchTerm}
                      onChange={(e) => {
                        const newSearchTerm = e.target.value;
                        setUserSearchTerm(newSearchTerm);

                        // Debounce the API call to avoid too many requests
                        if (window.userSearchTimeout) {
                          clearTimeout(window.userSearchTimeout);
                        }

                        window.userSearchTimeout = setTimeout(() => {
                          // Call the API with the search term
                          fetchUsers(newSearchTerm);
                        }, 300); // 300ms debounce
                      }}
                      className="w-full pl-9 pr-3 py-2 border border-gray-300 rounded-md"
                    />
                    {loadingUsers ? (
                      <div className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 border-2 border-t-transparent border-blue-500 rounded-full animate-spin" />
                    ) : (
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    )}
                    {userSearchTerm && (
                      <button
                        onClick={() => {
                          setUserSearchTerm("");
                          // Clear the search and fetch all users
                          fetchUsers("");
                        }}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    )}
                  </div>

                  {
                    <>
                      {availableUsers.length === 0 ? (
                        <div className="text-center py-4 text-gray-500">
                          No users available
                        </div>
                      ) : (
                        <div className="max-h-60 overflow-y-auto border border-gray-300 rounded-md">
                          {availableUsers.map((user) => {
                            return (
                              <div
                                key={user.id}
                                className={`flex items-center p-2 hover:bg-gray-50 border-b border-gray-200 ${
                                  selectedUsers.some((u) => u.id === user.id) ? "bg-blue-50" : ""
                                }`}
                              >
                                <input
                                  type="radio"
                                  id={`user-${user.id}`}
                                  name="selectedUser"
                                  checked={selectedUsers.some(
                                    (u) => u.id === user.id
                                  )}
                                  onChange={() => {
                                    // Radio buttons allow only one selection
                                    setSelectedUsers([user]);
                                  }}
                                  className="h-4 w-4 text-blue-600 border-gray-300"
                                />
                                <label
                                  htmlFor={`user-${user.id}`}
                                  className="ml-2 block text-sm text-gray-700 cursor-pointer flex-1"
                                >
                                  <div className="font-medium flex items-center">
                                    {user.name}
                                  </div>
                                </label>
                              </div>
                            );
                          })}
                        </div>
                      )}

                      {selectedUsers.length > 0 && (
                        <div className="mt-3 p-3 bg-blue-50 rounded-md border border-blue-100">
                          <div className="text-sm font-medium text-blue-700 mb-2">
                            Selected User
                          </div>
                          <div className="flex items-center bg-white text-blue-800 px-3 py-2 rounded-md border border-blue-200">
                            <User className="w-4 h-4 mr-2 text-blue-600" />
                            <div className="flex-1">
                              <div className="font-medium">
                                {selectedUsers[0].name}
                              </div>

                            </div>
                            <button
                              onClick={() => setSelectedUsers([])}
                              className="ml-2 text-blue-600 hover:text-blue-800 p-1 hover:bg-blue-50 rounded-full"
                            >
                              <X className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      )}
                    </>
                  }
                </>
              )}
            </div>
          </div>
        </Modal>

        {/* Admin Assignment Modal */}
        <Modal
          isOpen={isAdminAssignModalOpen}
          onClose={() => setIsAdminAssignModalOpen(false)}
          title="Assign Admin to Hierarchy"
          onOk={handleAssignAdmins}
          // Custom footer with unassign button
          footer={
            <div className="flex justify-end space-x-2">
              {/* Show only Unassign button when an admin is assigned */}
              {nodesWithAssignedAdmins[selectedNodeId] &&
                nodesWithAssignedAdmins[selectedNodeId].length > 0 ? (
                  <>
                    <button
                      onClick={() => setIsAdminAssignModalOpen(false)}
                      className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-md"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={async () => {
                        try {
                          // Prepare for unassignment
                          // The hierarchyId is passed directly to the unassignAdminFromNode function

                          // Call the hierarchyService method
                          const response = await hierarchyService.unassignAdminFromNode(selectedNodeId);

                          if (response) {
                            // Update the local state to reflect unassigned admin
                            setNodesWithAssignedAdmins((prev) => ({
                              ...prev,
                              [selectedNodeId]: [],
                            }));
                            setSelectedAdminUsers([]);

                            // Close the modal
                            setIsAdminAssignModalOpen(false);
                          } else {
                            alert("Failed to unassign admin. Please try again.");
                          }
                        } catch (error) {
                          // Even if there's an error, update the UI for better user experience
                          setNodesWithAssignedAdmins((prev) => ({
                            ...prev,
                            [selectedNodeId]: [],
                          }));
                          setSelectedAdminUsers([]);

                          // Close the modal
                          setIsAdminAssignModalOpen(false);

                          alert("An error occurred while unassigning admin, but the UI has been updated locally.");
                        }
                      }}
                      className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600"
                    >
                      Unassign Admin
                    </button>
                  </>
                ) : (
                  <>
                    <button
                      onClick={() => setIsAdminAssignModalOpen(false)}
                      className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-md"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleAssignAdmins}
                      className="px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600"
                      disabled={selectedAdminUsers.length === 0}
                    >
                      Assign Admin
                    </button>
                  </>
                )}
            </div>
          }
        >
          <div className="space-y-4">
            {selectedNode && (
              <div className="bg-blue-50 p-3 rounded-md mb-4">
                <h3 className="font-medium text-blue-800">Hierarchy Details</h3>
                <div className="mt-2 text-sm text-blue-700">
                  <p>
                    <span className="font-medium">Hierarchy Name:</span>{" "}
                    {selectedNode.name}
                  </p>
                  <p>
                    <span className="font-medium">Hierarchy Code:</span>{" "}
                    {selectedNode.code || "N/A"}
                  </p>
                  <p>
                    <span className="font-medium">Category:</span>{" "}
                    {getNodeType(selectedNode.category)}
                  </p>
                </div>
              </div>
            )}

            <div>
              {/* Current assignment status */}
              {nodesWithAssignedAdmins[selectedNodeId] &&
              nodesWithAssignedAdmins[selectedNodeId].length > 0 ? (
                <div className="mb-4 p-3 bg-purple-50 rounded-md border border-purple-100">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="bg-purple-100 p-2 rounded-full mr-3">
                        <Users className="w-5 h-5 text-purple-600" />
                      </div>
                      <div>
                        <div className="text-sm font-medium text-purple-800">
                          Currently Assigned Admin
                        </div>
                        <div className="font-medium text-purple-700">
                          {nodesWithAssignedAdmins[selectedNodeId][0].name}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="mb-4 p-3 bg-gray-50 rounded-md border border-gray-200">
                  <div className="flex items-center">
                    <div className="bg-gray-100 p-2 rounded-full mr-3">
                      <Users className="w-5 h-5 text-gray-500" />
                    </div>
                    <div>
                      <div className="text-sm font-medium text-gray-700">
                        No Admin Assigned
                      </div>
                      <div className="text-xs text-gray-500">
                        Select a user below to assign as admin to this hierarchy
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Only show user search and selection when no admin is assigned */}
              {!(nodesWithAssignedAdmins[selectedNodeId] && nodesWithAssignedAdmins[selectedNodeId].length > 0) && (
                <>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Select Admin User
                  </label>

                  {/* Search input with autocomplete */}
                  <div className="relative mb-3">
                    <input
                      type="text"
                      placeholder="Search users by username..."
                      value={adminSearchTerm}
                      onChange={(e) => {
                        const newSearchTerm = e.target.value;
                        setAdminSearchTerm(newSearchTerm);

                        // Debounce the API call to avoid too many requests
                        if (window.adminSearchTimeout) {
                          clearTimeout(window.adminSearchTimeout);
                        }

                        window.adminSearchTimeout = setTimeout(() => {
                          // Call the API with the search term
                          fetchUsers(newSearchTerm);
                        }, 300); // 300ms debounce
                      }}
                      className="w-full pl-9 pr-3 py-2 border border-gray-300 rounded-md"
                    />
                    {loadingUsers ? (
                      <div className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 border-2 border-t-transparent border-purple-500 rounded-full animate-spin" />
                    ) : (
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    )}
                    {adminSearchTerm && (
                      <button
                        onClick={() => {
                          setAdminSearchTerm("");
                          // Clear the search and fetch all users
                          fetchUsers("");
                        }}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    )}
                  </div>

                  {
                    <>
                      {availableUsers.length === 0 ? (
                        <div className="text-center py-4 text-gray-500">
                          No users available
                        </div>
                      ) : (
                        <div className="max-h-60 overflow-y-auto border border-gray-300 rounded-md">
                          {availableUsers.map((user) => {
                            return (
                              <div
                                key={user.id}
                                className={`flex items-center p-2 hover:bg-gray-50 border-b border-gray-200 ${
                                  selectedAdminUsers.some((u) => u.id === user.id) ? "bg-purple-50" : ""
                                }`}
                              >
                                <input
                                  type="radio"
                                  id={`admin-${user.id}`}
                                  name="selectedAdmin"
                                  checked={selectedAdminUsers.some(
                                    (u) => u.id === user.id
                                  )}
                                  onChange={() => {
                                    // Radio buttons allow only one selection
                                    setSelectedAdminUsers([user]);
                                  }}
                                  className="h-4 w-4 text-purple-600 border-gray-300"
                                />
                                <label
                                  htmlFor={`admin-${user.id}`}
                                  className="ml-2 block text-sm text-gray-700 cursor-pointer flex-1"
                                >
                                  <div className="font-medium flex items-center">
                                    {user.name}
                                  </div>
                                </label>
                              </div>
                            );
                          })}
                        </div>
                      )}

                      {selectedAdminUsers.length > 0 && (
                        <div className="mt-3 p-3 bg-purple-50 rounded-md border border-purple-100">
                          <div className="text-sm font-medium text-purple-700 mb-2">
                            Selected Admin
                          </div>
                          <div className="flex items-center bg-white text-purple-800 px-3 py-2 rounded-md border border-purple-200">
                            <Users className="w-4 h-4 mr-2 text-purple-600" />
                            <div className="flex-1">
                              <div className="font-medium">
                                {selectedAdminUsers[0].name}
                              </div>
                            </div>
                            <button
                              onClick={() => setSelectedAdminUsers([])}
                              className="ml-2 text-purple-600 hover:text-purple-800 p-1 hover:bg-purple-50 rounded-full"
                            >
                              <X className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      )}
                    </>
                  }
                </>
              )}
            </div>
          </div>
        </Modal>
      </div>
    </div>
  );
}
