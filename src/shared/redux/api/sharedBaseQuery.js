import { fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { logout } from '../../../domains/authentication/redux/slices/AuthSlice'; // Import logout action if you are using Redux for auth

const getToken = () => {
    return localStorage.getItem('accessToken');
};

const sharedBaseQuery = fetchBaseQuery({
    baseUrl: import.meta.env.VITE_BASE_URI || "",
    prepareHeaders: (headers, { getState }) => {
        console.log("getState",getState)
        const token = getToken();  
        if (token) {
            headers.set("Authorization", `Bearer ${token}`);
        }

        return headers;
    }
});

// Extend sharedBaseQuery with error handling
export const baseQueryWithErrorHandling = async (args, api, extraOptions) => {
    let result = await sharedBaseQuery(args, api, extraOptions);

    if (result.error && result.error.status === 401) {
        api.dispatch(logout());
        alert("Session expired. Please log in again.");
    }

    return result;
};
