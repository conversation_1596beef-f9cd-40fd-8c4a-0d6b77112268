/**
 * Hierarchy Management API URLs
 * This file centralizes all API endpoints related to the hierarchy management system
 */

import { baseUrl } from './baseUrl';

// Base URL for hierarchy management endpoints
export const HIERARCHY_BASE_URL = `${baseUrl}/api/v1/master`;

// Export the base URL for direct access
export const HIERARCHY_API_BASE = HIERARCHY_BASE_URL;

// Hierarchy endpoints
export const HIERARCHY_ENDPOINTS = {
  // Get children nodes by parent ID
  GET_CHILDREN: `${HIERARCHY_BASE_URL}/children`,

  // Create new hierarchy node
  CREATE_NODE: `${HIERARCHY_BASE_URL}/setup`,

  // Update hierarchy node
  UPDATE_NODE: `${HIERARCHY_BASE_URL}/setup`,

  // Delete hierarchy node
  DELETE_NODE: `${HIERARCHY_BASE_URL}/setup`,

  // Get users for assignment
  GET_USERS: `${HIERARCHY_BASE_URL}/users`,

  // Assign user to node
  ASSIGN_USER: `${HIERARCHY_BASE_URL}/assign-user`,

  // Unassign user from node
  UNASSIGN_USER: `${HIERARCHY_BASE_URL}/unassign-user`,
};

export default HIERARCHY_ENDPOINTS;
