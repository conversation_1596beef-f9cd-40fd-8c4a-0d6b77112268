import { createSlice } from "@reduxjs/toolkit";
// Importing constants
import {
  LAYOUT_TYPES,
  LAYOUT_SEMI_DARK,
  LAYOUT_SKIN,
  LAYOUT_MODE_TYPES,
  LAYOUT_DIRECTION,
  LAYOUT_CONTENT_WIDTH,
  LEFT_SIDEBAR_SIZE_TYPES,
  LEFT_NAVIGATION_TYPES,
  LEFT_SIDEBAR_COLOR_TYPES,
  LAYOUT_TOPBAR_THEME_TYPES
} from "../../../constants/layout";

// Initial state for the layout
const initialState = {
  layoutType: LAYOUT_TYPES.HORIZONTAL,
  layoutSemiDarkType: LAYOUT_SEMI_DARK.LIGHT,
  layoutSkintype: LAYOUT_SKIN.DEFAULT,
  layoutModeType: LAYOUT_MODE_TYPES.LIGHTMODE,
  layoutDirectionType: LAYOUT_DIRECTION.LTR,
  layoutContentWidthType: LAYOUT_CONTENT_WIDTH.FLUID,
  layoutSidebarSizeType: LEFT_SIDEBAR_SIZE_TYPES.DEFAULT,
  layoutNavigationType: LEFT_NAVIGATION_TYPES.STICKY,
  layoutSidebarColorType: LEFT_SIDEBAR_COLOR_TYPES.LIGHT,
  layoutTopbarColorType: LAYOUT_TOPBAR_THEME_TYPES.BRAND,
};

// Creating the layout slice
const LayoutSlice = createSlice({
  name: 'LayoutSlice',
  initialState,
  reducers: {
    changeLayoutAction(state, action) {
      state.layoutType = action.payload;
    },
    changeLayoutSemiDarkAction(state, action) {
      state.layoutSemiDarkType = action.payload;
    },
    changeSkinAction(state, action) {
      state.layoutSkintype = action.payload;
    },
    changeLayoutModeAction(state, action) {
      state.layoutModeType = action.payload;
    },
    changeDirectionAction(state, action) {
      state.layoutDirectionType = action.payload;
    },
    changeLayoutContentWidthAction(state, action) {
      state.layoutContentWidthType = action.payload;
    },
    changeLayoutSidebarSizeAction(state, action) {
      state.layoutSidebarSizeType = action.payload;
    },
    changeNavigationAction(state, action) {
      state.layoutNavigationType = action.payload;
    },
    changeLeftSidebarColorTypeAction(state, action) {
      state.layoutSidebarColorType = action.payload;
    },
    changeLayoutTopbarColorAction(state, action) {
      state.layoutTopbarColorType = action.payload;
    }
  }
});

// Exporting the actions and the reducer
export const {
  changeLayoutAction,
  changeLayoutSemiDarkAction,
  changeSkinAction,
  changeLayoutModeAction,
  changeDirectionAction,
  changeLayoutContentWidthAction,
  changeLayoutSidebarSizeAction,
  changeNavigationAction,
  changeLeftSidebarColorTypeAction,
  changeLayoutTopbarColorAction
} = LayoutSlice.actions;

export default LayoutSlice.reducer;
