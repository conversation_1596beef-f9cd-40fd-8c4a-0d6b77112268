import { changeHTMLAttribute } from './utils';
import {
    changeLayoutAction,
    changeLayoutSemiDarkAction,
    changeSkinAction,
    changeLayoutModeAction,
    changeDirectionAction,
    changeLayoutContentWidthAction,
    changeLayoutSidebarSizeAction,
    changeNavigationAction,
    changeLeftSidebarColorTypeAction,
    changeLayoutTopbarColorAction
} from './layoutSlice';

/**
 * Changes the layout type
 */
export const changeLayout = (layout) => async (dispatch) => {
    try {
        changeHTMLAttribute("data-layout", layout);
        dispatch(changeLayoutAction(layout));
    } catch (error) { 
        console.log(error);
    }
};

/**
 * Changes the left Semi Dark (Sidebar & Header)
 */
export const changeLayoutSemiDark = (sidebarTheme) => async (dispatch) => {
    try {
        changeHTMLAttribute("data-topbar", sidebarTheme);
        dispatch(changeLayoutSemiDarkAction(sidebarTheme));
    } catch (error) {
        console.log(error);
    }
};

/**
 * Changes the Layout Skin Theme
 */
export const changeSkin = (skinTheme) => async (dispatch) => {
    try {
        changeHTMLAttribute("data-skin", skinTheme);
        dispatch(changeSkinAction(skinTheme));
    } catch (error) {
        console.log(error);
    }
};

/**
 * Changes the layout mode 
 */
export const changeLayoutMode = (layoutMode) => async (dispatch) => {
    try {
        changeHTMLAttribute("data-mode", layoutMode);
        dispatch(changeLayoutModeAction(layoutMode));
    } catch (error) {
        console.log(error);
     }
};

/**
 * Changes the layout direction
 */
export const changeDirection = (direction) => async (dispatch) => {
    try {
        changeHTMLAttribute("dir", direction);
        dispatch(changeDirectionAction(direction));
    } catch (error) {
        console.log(error);
    }
};

/**
 * Changes the Content width
 */
export const changeLayoutContentWidth = (contentWidth) => async (dispatch) => {
    try {
        changeHTMLAttribute("data-content", contentWidth);
        dispatch(changeLayoutContentWidthAction(contentWidth));
    } catch (error) {
        console.log(error);
    }
};

/**
 * Changes the left sidebar size
 */
export const changeLeftsidebarSizeType = (leftsidebarSizeType) => async (dispatch) => {
    try {
        switch (leftsidebarSizeType) {
            case 'lg':
                changeHTMLAttribute("data-sidebar-size", "lg");
                break;
            case 'md':
                changeHTMLAttribute("data-sidebar-size", "md");
                break;
            case "sm":
                changeHTMLAttribute("data-sidebar-size", "sm");
                break;
            default:
                changeHTMLAttribute("data-sidebar-size", "lg");
        }
        dispatch(changeLayoutSidebarSizeAction(leftsidebarSizeType));

    } catch (error) {
        console.log(error);
    }
};

/**
 * Changes the Navigation
 */
export const changeNavigation = (navigation) => async (dispatch) => {
    try {
        changeHTMLAttribute("data-navbar", navigation);
        dispatch(changeNavigationAction(navigation));
    } catch (error) {
        console.log(error);
    }
};

/**
 * Changes the Sidebar Color
 */
export const changeLeftSidebarColorType = (sidebarColor) => async (dispatch) => {
    try {
        changeHTMLAttribute("data-sidebar", sidebarColor);
        dispatch(changeLeftSidebarColorTypeAction(sidebarColor));
    } catch (error) {
        console.log(error);
    }
};

/**
 * Changes the Topbar Color
 */
export const changeLayoutTopbarColor = (topbarColor) => async (dispatch) => {
    try {
        changeHTMLAttribute("data-topbar", topbarColor);
        dispatch(changeLayoutTopbarColorAction(topbarColor));
    } catch (error) {
        console.log(error);
    }
};
