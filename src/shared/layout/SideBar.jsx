import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';

const ShrinkableSidebar = () => {
  const location = useLocation();
  const { user } = useSelector((state) => state.User);
  const [collapsed, setCollapsed] = useState(false);
  
  // Menu data structure - match with your application routes
  const menuItems = [
    // {
    //   id: 'dashboards',
    //   label: 'Dashboards',
    //   icon: 'fa-tachometer-alt',
    //   link: '/dashboards-overview',
    // },
    // {
    //   id: 'audit',
    //   label: 'Audit',
    //   icon: 'fa-clipboard-check',
    //   subItems: [
    //     { label: 'Initiated Product', link: '/initiated-product' },
    //     { label: 'Submitted Product', link: '/submitted-product' },
    //     { label: 'Replied Product', link: '/replied-product' },
    //     { label: 'Received Product', link: '/received-product' },
    //     { label: 'Add New Audit', link: '/add-new-audit' },
    //   ],
    // },
    {
      id: 'admin',
      label: 'Admin',
      icon: 'fa-cog',
      subItems: [
        { label: 'User Setup', link: '/smart-admin' },
        // Add more admin pages as needed
      ],
    },
  ];

  // State for tracking open/collapsed menu sections
  const [openMenus, setOpenMenus] = useState({});

  // Toggle submenu visibility
  const toggleMenu = (id) => {
    setOpenMenus(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  // Check if a menu item should be shown as active
  const isActive = (link) => {
    return location.pathname === link;
  };

  // Check if any child in a submenu is active
  const hasActiveChild = (subItems) => {
    return subItems && subItems.some(item => isActive(item.link));
  };

  // Toggle sidebar collapse state
  const toggleSidebar = () => {
    setCollapsed(!collapsed);
  };

  // Close all submenus when sidebar is collapsed
  useEffect(() => {
    if (collapsed) {
      setOpenMenus({});
    }
  }, [collapsed]);

  return (
    <div className={`fixed top-0 left-0 bottom-0 z-50 bg-white dark:bg-zink-700 border-r border-slate-200 dark:border-zink-500 overflow-hidden transition-all duration-300 print:hidden ${collapsed ? 'w-16' : 'w-64'}`}>
      {/* Logo section */}
      <div className="lg:h-header flex items-center sticky top-0 left-0 z-40 bg-white dark:bg-zink-700 border-b border-slate-200 dark:border-zink-500 px-4">
        <div className="flex items-center gap-3">
          <img 
            src="/logo.png" 
            alt="Logo" 
            className="h-8" 
          />
          <span className={`font-semibold text-lg transition-opacity duration-300 ${collapsed ? 'opacity-0' : 'opacity-100'}`}>
            Smart Admin
          </span>
        </div>
        
        {/* Toggle button */}
        <button 
          onClick={toggleSidebar}
          className="ml-auto text-slate-500 hover:text-slate-700 dark:text-zink-200 dark:hover:text-white"
          aria-label={collapsed ? "Expand sidebar" : "Collapse sidebar"}
        >
          <i className={`fas ${collapsed ? 'fa-chevron-right' : 'fa-chevron-left'} text-lg`}></i>
        </button>
      </div>

      {/* Menu section */}
      <div className="h-[calc(100vh-theme('spacing.header'))] overflow-y-auto overflow-x-hidden px-2 py-4">
        <ul className="flex flex-col gap-1.5">
          {menuItems.map((item) => (
            <li key={item.id} className="menu-item">
              {item.subItems ? (
                // Menu item with dropdown
                <>
                  <button
                    onClick={() => toggleMenu(item.id)}
                    className={`group/menu flex items-center gap-3 py-2 px-3 rounded-md w-full hover:bg-slate-100 dark:hover:bg-zink-600 ${(openMenus[item.id] || hasActiveChild(item.subItems)) ? 'bg-slate-100 dark:bg-zink-600' : ''}`}
                  >
                    <i className={`fas ${item.icon} text-lg min-w-6 text-center`}></i>
                    <span className={`text-15 truncate transition-opacity duration-300 ${collapsed ? 'opacity-0 w-0' : 'opacity-100'}`}>
                      {item.label}
                    </span>
                    {!collapsed && (
                      <i className={`fas fa-chevron-down ml-auto transition-transform duration-200 ${openMenus[item.id] ? 'rotate-180' : ''}`}></i>
                    )}
                  </button>
                  
                  {/* Submenu - only show when sidebar is expanded */}
                  {!collapsed && (
                    <div className={`transition-all duration-300 ease-in-out ${openMenus[item.id] ? 'max-h-96' : 'max-h-0 overflow-hidden'}`}>
                      <ul className="pl-8 pr-2 mt-1 space-y-1">
                        {item.subItems.map((subItem, idx) => (
                          <li key={idx}>
                            <Link
                              to={subItem.link}
                              className={`flex items-center py-2 px-3 rounded-md group/menu hover:bg-slate-100 dark:hover:bg-zink-600 ${isActive(subItem.link) ? 'bg-slate-100 dark:bg-zink-600 text-red-600 font-medium' : ''}`}
                            >
                              <span className="w-1.5 h-1.5 rounded-full bg-slate-400 dark:bg-zink-400 mr-2"></span>
                              <span className="text-15 truncate">
                                {subItem.label}
                              </span>
                            </Link>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </>
              ) : (
                // Single menu item
                <Link
                  to={item.link}
                  className={`flex items-center gap-3 py-2 px-3 rounded-md hover:bg-slate-100 dark:hover:bg-zink-600 ${isActive(item.link) ? 'bg-slate-100 dark:bg-zink-600 text-red-600 font-medium' : ''}`}
                >
                  <i className={`fas ${item.icon} text-lg min-w-6 text-center`}></i>
                  <span className={`text-15 truncate transition-opacity duration-300 ${collapsed ? 'opacity-0 w-0' : 'opacity-100'}`}>
                    {item.label}
                  </span>
                </Link>
              )}
            </li>
          ))}
        </ul>
      </div>
      
      {/* Show tooltips when sidebar is collapsed - hover handler */}
      {collapsed && (
        <div className="absolute top-0 left-16 z-50">
          {menuItems.map((item) => (
            <div key={`tooltip-${item.id}`} className="tooltip-container">
              {/* Tooltip implementation can be added here */}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ShrinkableSidebar;