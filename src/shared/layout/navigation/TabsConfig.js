import { useSelector } from 'react-redux';
import { menuData } from './LayoutMenuData';

const fetchUserPrivileges = (state) => {
    const userDetails = state.User?.userDetails || {};
    const modules = userDetails.userModules || [];
    const moduleId = 1;
    const module = modules.find((module) => module.id === moduleId);
    return module ? module.privileges.map((privilege) => privilege.privilegeCode) : [];
};

// Function to filter menu items by privileges
const filterMenuDataByPrivileges = (menuData, userPrivileges) => {
    if (userPrivileges.length === 0) {
        // No privileges, return the entire menu
        return menuData;
    }
    return menuData
        .map(item => {
            // if (!item.privilege || userPrivileges.includes(item.privilege)) {
            //     // Filter subItems if any, based on privileges
            //     if (item.subItems) {
            //         item.subItems = item.subItems.filter(
            //             subItem => !subItem.privilege || userPrivileges.includes(subItem.privilege)
            //         );
            //     }
                return item;
            // }
            // return null; 
        })
        .filter(Boolean); 
};

const useFilteredMenuData = () => {
    const userPrivileges = useSelector(fetchUserPrivileges);
    console.log(userPrivileges, menuData, 'userPrivileges2025');
    return filterMenuDataByPrivileges(menuData, userPrivileges);
};

export { useFilteredMenuData };
