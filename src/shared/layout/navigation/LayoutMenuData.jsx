import { Bell, BookOpenText, CreditCard, GitBranch, MonitorDot, NotebookTabs, User } from "lucide-react";
const menuData = [
    {
        label: 'menu',
        isTitle: true,
    },
    {
        id: "hierarchy",
        label: 'Hierarchy',
        link: "/smart-admin/hierarchy",
        icon: <GitBranch size={20} />,
    },
    {
        id: "smart-admin",
        label: 'Users',
        link: "#",
        icon: <User size={20} />,
        subItems: [
            { id: 'userList', label: 'User Setup', link: '/smart-admin/userList', parentId: "smart-admin"},
            { id: 'externalUserList', label: 'External Users', link: '/smart-admin/externalUserList', parentId: "smart-admin" },
            { id: 'externalUserApproval', label: 'External User Approval', link: '/smart-admin/externalUserListForApproval', parentId: "smart-admin" },

        ]
    },
    {
    id: "posting",
    label: 'Posting',
        link: '/smart-admin/posting',
        icon: <CreditCard size={20} />
},
{
    id: "modules",
    label: 'Modules',
        link: '/smart-admin/modules',
        icon: <MonitorDot size={20} />
},
{
    id: "privilege",
    label: 'Privilege',
        link: '/smart-admin/privilege',
        icon: <NotebookTabs size={20} />
},
  
    {
        id: "notifiaction",
        label: 'Notification',
        link: "/smart-admin/notification",
        icon: <Bell size={20} />,
    },
];

export { menuData };
// export default Dropdown;
