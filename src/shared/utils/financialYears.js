export const getFinancialYears = (numYears) => {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-based

    const startYear = currentMonth >= 4 ? currentYear : currentYear - 1;

    const financialYears = [];
    for (let i = 0; i < numYears; i++) {
        const endYear = startYear - i + 1;
        const finYear = `${startYear - i}-${endYear.toString().slice(-2)}`;
        financialYears.push(finYear);
    }

    return financialYears;
};

export const getCurrentFinYear = ()=>{
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1;
    const startYear = currentMonth >= 4 ? currentYear : currentYear - 1;
    const endYear = startYear + 1;
    const currentFinYear = `${startYear}-${endYear.toString().slice(-2)}`;
    return currentFinYear;
}