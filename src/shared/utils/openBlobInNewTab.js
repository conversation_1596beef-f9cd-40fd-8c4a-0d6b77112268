// Utility to open a Blob in a new browser tab
export const openBlobInNewTab = (response) => {
  try {

    const blob = new Blob([response], { type: 'application/pdf' });
    const blobUrl = URL.createObjectURL(blob);
    const newTab = window.open(blobUrl, '_blank');
    if (!newTab) {
      console.error('Failed to open new tab. Check browser popup settings.');
    }
    // Cleanup blob URL after the new tab is loaded
    if (newTab) {
      newTab.onload = () => URL.revokeObjectURL(blobUrl);
    }
  } catch (error) {
    console.error('Error opening blob in new tab:', error);
  }
};