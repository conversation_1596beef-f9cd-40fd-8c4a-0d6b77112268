import axios from "axios";
import { useNavigate } from "react-router-dom";

const axiosInstance = axios.create({
  baseURL: import.meta.env.VITE_BASE_URI || "", 
  headers: {
    "Content-Type": "application/json",
  },
  timeout: 60000,  // 60-second timeout to handle unresponsive requests
});

// Set the Authorization token if available
const token = localStorage.getItem("accessToken");
if (token) {
  axiosInstance.defaults.headers.common["Authorization"] = "Bearer " + token;
}

// Error handling interceptor
axiosInstance.interceptors.response.use(
  response => response.data || response,
  error => {
    const navigate = useNavigate();
    let message;
    const status = error.response ? error.response.status : null;
    switch (status) {
      case 500:
        message = "Something went wrong on our end. Please try again later.";
        break;
      case 401:
        message = "Invalid credentials. Please log in again.";
        break;
      case 404:
        message = "The requested data could not be found.";
        break;
      default:
        message = error.response?.data?.message || "An unexpected error occurred.";
    }
    return Promise.reject(message);
  }
);

// Function to set or update Authorization token
const setAuthorization = (token) => {
  axiosInstance.defaults.headers.common["Authorization"] = "Bearer " + token;
};

// API Client Class
class APIClient {
  constructor(axiosInstance) {
    this.axios = axiosInstance;
  }

  // GET request with optional query parameters
  get = (url, params = {}, config = {}) => {
    const filteredParams = Object.fromEntries(
      // eslint-disable-next-line no-unused-vars
      Object.entries(params).filter(([_, value]) => value != null)
  );
    const queryString = new URLSearchParams(filteredParams).toString();
    return this.axios.get(queryString ? `${url}?${queryString}` : url,config);
  };

  // POST request to create new data
  create = (url, data) => this.axios.post(url, data).then(response=> response);

  // PATCH request to update data partially
  update = (url, data) => this.axios.patch(url, data);

  // PUT request to replace data entirely
  put = (url, data) => this.axios.put(url, data);

  // DELETE request with optional configuration
  delete = (url, config = {}) => this.axios.delete(url, config);
}

// Initialize the API client with the Axios instance
const apiClient = new APIClient(axiosInstance);

export { apiClient, setAuthorization };
