// AUDIT-SERVICE

export const GET_PENDING_LIST = "/audit-service/pendingInspectionReportBaseListForAG"
export const GET_SUBMITTED_LIST = "/audit-service/submittedProposalListAG"
export const GET_REPLIED_LIST = "/audit-service/replayedProposalListAG"
export const GET_RECEIVED_LIST = "/audit-service/proposalListReceivedForUser"

export const SAVE_INSPECTION_REPORT = "/audit-service/createInspectionReportBase"
export const GET_INSPECTION_REPORT = "/audit-service/getInspectionReportBase"
export const DOWNLOAD_INSPECTION_REPORT = "/audit-service/generateConsolidatedPDFForAG"

export const SAVE_OBSERVATION = "/audit-service/updateInspectionReportDetails"
export const GET_OBSERVATION_LIST = "/audit-service/getInspectionReportDetails"
export const SAVE_AUDIT_OBSERVATION = "/audit-service/inspection-report-observations"

export const GET_NOTES = "/audit-service/notes"
export const SAVE_NOTES = "/audit-service/notes"

export const AUDIT_HISTORY = "/audit-service/getAuditProposalHistory"
export const SUBMIT_AUDIT = "/audit-service/submitProposalToUser"


export const AUTH_REST_API_BASE_URL = "https://cgifms.kran.co.in/cas/oidc/token";
export const PROFILE_API_BASE_URL = "https://cgifms.kran.co.in/cas/oidc/profile";



// GENERAL-SERVICE

export const GET_USER_DETAILS = "/general-service/user/getUserDetailsAndModulePrivileges"

export const SAVE_QUERY = "/queryForInspectionReportPara"
export const QUERIED_DATA = "/getAllInspectionReportQuery"

export const AUTH_TOKEN_INTROSPECT = "/introspect"

// HIERARCHY MANAGEMENT
// Get children nodes by parent ID
export const GET_CHILDREN = `/children`;

// Create new hierarchy node
export const CREATE_NODE = `/hierarchy`;

// Update hierarchy node
export const UPDATE_NODE = `/setup`;

// Delete hierarchy node
export const DELETE_NODE = `/setup`;

// Get users for assignment
export const GET_USERS = `/users`;

// Assign user to node
export const ASSIGN_USER = `/hierarchy/assign-user`;

// Unassign user from node
export const UNASSIGN_USER = `/hierarchy/unassign-user`;

// Assign admin to node
export const ASSIGN_ADMIN = `/hierarchy/assign-admin`;

// Unassign admin from node
export const UNASSIGN_ADMIN = `/hierarchy/unassign-admin`;

// Move node to a new parent
export const MOVE_NODE = `/transferHierarchy`;

// Get hierarchy data by ID and type
export const GET_HIERARCHY_DATA = `/hierarchy`;


