import { useNavigate } from "react-router-dom";

export default function ServerError() {
    const navigate = useNavigate();
    
    const handleGoHome = () => {
        navigate('/');
    };
    
    return (
        <div className="flex flex-col items-center justify-center p-8 bg-white shadow-md rounded-md max-w-md mx-auto">
            <img
                src="/images/server-error.png"
                alt="500 Server Error"
                className="w-full max-w-xs md:max-w-sm mb-6"
            />
            <h1 className="text-2xl font-bold mb-2">Oops! Something went wrong.</h1>
            <p className="text-center text-gray-600 mb-6">
                The server encountered an internal error and was unable to complete your request.
            </p>
            
            <button
                onClick={handleGoHome}
                className="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded"
            >
                Go Home
            </button>
        </div>
    );
}
