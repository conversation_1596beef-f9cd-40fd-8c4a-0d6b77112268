import errorImage from "../../shared/assets/images/errors/404.svg";
const NotFoundError = () => {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50 overflow-hidden px-4">
      {/* Image Container */}
      <div className="relative flex items-center justify-center mb-10">
        <img
          src={errorImage}
          alt="404 Error"
          className="w-full max-w-[30rem] md:max-w-[40rem] lg:max-w-[50rem] h-auto"
        />
      </div>

      {/* Title */}
      <h1 className="text-3xl md:text-4xl lg:text-5xl font-extrabold text-center leading-tight">
        Uh-oh! This Page Can’t Be Found
      </h1>

      {/* Description */}
      <p className="mt-4 text-gray-600 text-center text-sm md:text-base lg:text-lg px-2 md:px-6 lg:px-12">
        Oops! The page you're looking for is unavailable. We'll have it fixed
        soon. For queries, contact us at{" "}
        <a
          href="mailto:<EMAIL>"
          className="text-teal-600 hover:underline"
        >
          <EMAIL>
        </a>
      </p>

      {/* Back to Home Button */}
      <div className="mt-6">
        <a
          href="/"
          className="bg-teal-600 hover:bg-teal-700 text-white font-bold py-3 px-6 rounded-md transition duration-300"
        >
          Back to Home
        </a>
      </div>
    </div>
  );
};

export default NotFoundError;
