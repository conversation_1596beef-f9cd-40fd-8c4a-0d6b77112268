import errorImage from '../../shared/assets/images/errors/401.svg'
const AccessDenied = () => {
    return (
        <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50 text-gray-700">
            {/* 401 Error Heading */}
            <h1 className="text-4xl font-bold mb-4">Access Denied!</h1>

            {/* Description */}
            <p className="text-lg mb-6">
            Your session has expired. Please log in again to continue!!!
                <a
                    href="mailto:<EMAIL>"
                    className="text-green-600 hover:underline"
                >
                     cgifms.kran.co.in
                </a>{" "}
            </p>

            {/* 401 and Image */}
            <div className="relative flex items-center justify-center">
                {/* <p className="absolute text-[200px] text-gray-100 font-bold">401</p> */}
               

                 <img
                          src={errorImage}
                          alt="Access Denied"
                          className="w-full max-w-[30rem] md:max-w-[40rem] lg:max-w-[50rem] h-auto"
                        />
            </div>
        </div>
    );
}

export default AccessDenied;
