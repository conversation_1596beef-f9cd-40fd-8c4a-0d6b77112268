import { BellDot, Clock, MoveRight, ShoppingBag } from 'lucide-react';
import { useState } from 'react'
import { Link } from 'react-router-dom';
import SimpleBar from 'simplebar-react';
import { Dropdown } from './components/Dropdown';

const NotificationDropdown = () => {

    const notification = [
        { id: 1, type: "submitted",  user: "@FA, <PERSON>", action: "Submitted An Audit to Directorate", time: "4 sec", date: "Wednesday 03:42 PM" },
        { id: 2, type: "replied",  user: "@SFA, <PERSON> ", action: "Replied on your audit Observation", time: "15 min", description: "Replied! Department Replied a Compliance Audit !!!", date: "Wednesday 03:42 PM", },
        { id: 3, type: "queried",user: "@HOD, Safia N", action: "Queried on your audit Observation",  time: "Yesterday", date: "Monday 11:26 AM" },
        { id: 4, type: "replied", user: "@HOD, R<PERSON>reesh M", action: "Replied on your audit Observation", time: "1 Week", date: "Thursday 06:59 AM" },
    ]
    const [filter, setFilter] = useState("all");
    return (
        <>
            <Dropdown className="relative flex items-center h-header">
                <Dropdown.Trigger type="button" className="inline-flex justify-center relative items-center p-0 text-topbar-item transition-all size-[37.5px] duration-200 ease-linear bg-topbar rounded-md dropdown-toggle btn hover:bg-topbar-item-bg-hover hover:text-topbar-item-hover group-data-[topbar=dark]:bg-topbar-dark group-data-[topbar=dark]:hover:bg-topbar-item-bg-hover-dark group-data-[topbar=dark]:hover:text-topbar-item-hover-dark group-data-[topbar=brand]:bg-topbar-brand group-data-[topbar=brand]:hover:bg-topbar-item-bg-hover-brand group-data-[topbar=brand]:hover:text-topbar-item-hover-brand group-data-[topbar=dark]:dark:bg-zink-700 group-data-[topbar=dark]:dark:hover:bg-zink-600 group-data-[topbar=brand]:text-topbar-item-brand group-data-[topbar=dark]:dark:hover:text-zink-50 group-data-[topbar=dark]:dark:text-zink-200 group-data-[topbar=dark]:text-topbar-item-dark" id="notificationDropdown" data-bs-toggle="dropdown">
                    <BellDot className="inline-block size-5 stroke-1 fill-slate-100 group-data-[topbar=dark]:fill-topbar-item-bg-hover-dark group-data-[topbar=brand]:fill-topbar-item-bg-hover-brand" />
                    {/* <span className="absolute top-0 right-0 flex w-3 h-3">
                        <span className="absolute inline-flex w-full h-full rounded-full opacity-75 animate-ping bg-red-200"></span>
                        <span className="relative inline-flex w-3 h-3 rounded-full bg-red-400"></span>
                    </span> */}
                    <span className="absolute top-0 right-0 flex w-3.5 h-3.5">
                        <span className="absolute inline-flex w-full h-full rounded-full opacity-75 animate-ping bg-red-200"></span>
                        <span className="relative inline-flex w-3.5 h-3.5 rounded-full bg-red-400">
                            <span className="absolute inset-0 flex items-center justify-center text-xs text-white font-semibold">
                                3
                            </span>
                        </span>
                    </span>

                </Dropdown.Trigger>
                <Dropdown.Content placement="right-end" className="absolute z-50 ltr:text-left rtl:text-right bg-white rounded-md shadow-md !top-4 dropdown-menu min-w-[20rem] lg:min-w-[26rem] dark:bg-zink-600" aria-labelledby="notificationDropdown">
                    <div className="p-4">
                        <h6 className="mb-4 text-16">Notifications <span className="inline-flex items-center justify-center size-5 ml-1 text-[11px] font-medium border rounded-full text-white bg-orange-500 border-orange-500">3</span></h6>
                        <ul className="flex flex-wrap w-full p-1 mb-2 text-sm font-medium text-center rounded-md filter-btns text-slate-500 bg-slate-100 nav-tabs dark:bg-zink-500 dark:text-zink-200">
                            <li className={`group grow ${filter === "all" ? "active" : ""}`} onClick={() => setFilter("all")}>
                                <Link to="#" data-filter="all" className="inline-block nav-link px-1.5 w-full py-1 text-xs transition-all duration-300 ease-linear rounded-md text-slate-500 border border-transparent group-[.active]:bg-white group-[.active]:text-custom-500 hover:text-custom-500 active:text-custom-500 dark:text-zink-200 dark:hover:text-custom-500 dark:group-[.active]:bg-zink-600 -mb-[1px]">View All</Link>
                            </li>
                            <li className={`group grow ${filter === "replied" ? "active" : ""}`} onClick={() => setFilter("replied")}>
                                <Link to="#" data-filter="replied" className="inline-block nav-link px-1.5 w-full py-1 text-xs transition-all duration-300 ease-linear rounded-md text-slate-500 border border-transparent group-[.active]:bg-white group-[.active]:text-custom-500 hover:text-custom-500 active:text-custom-500 dark:text-zink-200 dark:hover:text-custom-500 dark:group-[.active]:bg-zink-600 -mb-[1px]">replied</Link>
                            </li>
                            <li className={`group grow ${filter === "submitted" ? "active" : ""}`} onClick={() => setFilter("submitted")}>
                                <Link to="#" data-filter="submitted" className="inline-block nav-link px-1.5 w-full py-1 text-xs transition-all duration-300 ease-linear rounded-md text-slate-500 border border-transparent group-[.active]:bg-white group-[.active]:text-custom-500 hover:text-custom-500 active:text-custom-500 dark:text-zink-200 dark:hover:text-custom-500 dark:group-[.active]:bg-zink-600 -mb-[1px]">submitted</Link>
                            </li>
                            <li className={`group grow ${filter === "queried" ? "active" : ""}`} onClick={() => setFilter("queried")}>
                                <Link to="#" data-filter="queried" className="inline-block nav-link px-1.5 w-full py-1 text-xs transition-all duration-300 ease-linear rounded-md text-slate-500 border border-transparent group-[.active]:bg-white group-[.active]:text-custom-500 hover:text-custom-500 active:text-custom-500 dark:text-zink-200 dark:hover:text-custom-500 dark:group-[.active]:bg-zink-600 -mb-[1px]">queried</Link>
                            </li>
                        </ul>

                    </div>
                    <SimpleBar className="max-h-[350px]">
                        <div className="flex flex-col gap-1">
                            {
                                (notification || []).filter((data) => filter === "all" || data.type === filter)?.map((item, index) => (
                                    <a key={index} href="#!" className="flex gap-3 p-4 product-item hover:bg-slate-50 dark:hover:bg-zink-500">
                                        {index === notification.length - 1 ?
                                            <div className="relative shrink-0">
                                                <div className="size-10 bg-pink-100 rounded-md">
                                                   <p>image</p>
                                                </div>
                                                <div className="absolute text-orange-500 -bottom-0.5 -right-0.5 text-16">
                                                    <i className="ri-heart-fill"></i>
                                                </div>
                                            </div> :
                                            item.image ? <div className={item.imageClassName}>
                                                <img src={item.image} alt="" className="rounded-md" />
                                            </div> :
                                                <div className="flex items-center justify-center size-10 bg-red-100 rounded-md shrink-0">
                                                    <ShoppingBag className="size-5 text-red-500 fill-red-200"></ShoppingBag>
                                                </div>
                                        }
                                        <div className="grow">
                                            <h6 className="mb-1 font-medium">{item.user && <b>{item.user}</b>} {item.action} </h6>
                                            <p className={`text-sm text-slate-500 dark:text-zink-300 ${index === notification.length - 3 ? "mb-3" : "mb-0"}`}>
                                                <Clock className="inline-block size-3 mr-1"></Clock> <span className="align-middle">{item.date}</span></p>
                                            {item.description && <div className="p-2 rounded bg-slate-100 text-slate-500 dark:bg-zink-500 dark:text-zink-300">{item.description}</div>}
                                        </div>
                                        <div className="flex items-center self-start gap-2 text-xs text-slate-500 shrink-0 dark:text-zink-300">
                                            <div className="w-1.5 h-1.5 bg-custom-500 rounded-full"></div> {item.time}
                                        </div>
                                    </a>
                                ))
                            }

                        </div>
                    </SimpleBar>
                    <div className="flex items-center gap-2 p-4 border-t border-slate-200 dark:border-zink-500">
                        <div className="grow">
                            <a href="#!">Manage Notification</a>
                        </div>
                        <div className="shrink-0">
                            <button type="button" className="px-2 py-1.5 text-xs text-white transition-all duration-200 ease-linear btn bg-custom-500 border-custom-500 hover:text-white hover:bg-custom-600 hover:border-custom-600 focus:text-white focus:bg-custom-600 focus:border-custom-600 focus:ring focus:ring-custom-100 active:text-white active:bg-custom-600 active:border-custom-600 active:ring active:ring-custom-100">View All Notification
                                <MoveRight className="inline-block size-3 ml-1"></MoveRight>
                            </button>
                        </div>
                    </div>
                </Dropdown.Content >
            </Dropdown >
        </>
    )
}

export default NotificationDropdown