import { Navigate, useRoutes } from "react-router-dom"
import AuthProtected from "./AuthProtected";
import InitiatedProduct from "../../domains/audit/pages/InitiatedProduct";
import Layout from "../../shared/layout";
import SubmittedProduct from "../../domains/audit/pages/submittedProduct";
import RepliedProduct from "../../domains/audit/pages/repliedProduct";
import LazyRoute from "./LazyRoute";
import React from "react";
import NotFound from "../../shared/errors/NotFoundError";
import Test2 from "../../domains/audit/pages/Test2";
import AuthRedirect from "../../domains/authentication/pages/AuthRedirect";
import AccessDenied from "../../shared/errors/AccessDenied";
import ReceivedProduct from "../../domains/audit/pages/ReceivedProduct";
import UserSetup from "../../domains/smartadmin/pages/Usersetup";
import HierarchyBrowser from "../../shared/components/tree/HierarchyBrowser";
import Notification from "../../shared/components/notification/NotificationModal";
import ExternalUser from "../../domains/smartadmin/pages/ExternalUser";
import ExternalUserApproval from "../../domains/smartadmin/pages/ExternalUserApproval";
import Posting from "../../domains/smartadmin/pages/Posting";
import ModuleSetup from "../../domains/smartadmin/pages/ModuleSetup";
import Privilege from "../../domains/smartadmin/pages/Privilege";

// Lazy-loaded components
const AddAuditProduct = React.lazy(() => import("../../domains/audit/pages/AddAuditProduct"));
const ViewAuditDetails = React.lazy(() => import("../../domains/audit/pages/AuditView"));

const AppRouter = () => {
    const routes = useRoutes([
      {
        path: "/",
        element: <Layout />,
        children: [
          { path: "initiated-product", element: <InitiatedProduct /> },

          {
            path: "smart-admin",
            children: [
              { path: "userList", element: <UserSetup /> },
              { path: "externalUserList", element: <ExternalUser /> },
              { path: "externalUserListForApproval", element: <ExternalUserApproval /> },
              { path: "hierarchy", element: <HierarchyBrowser /> },
              { path: "notification", element: <Notification /> },
              { path: "posting", element: <Posting />},
              { path: "modules", element: <ModuleSetup />},
              { path: "privilege", element: <Privilege />},
                
            ],
          },
          { path: "dashboards-overview", element: <Test2 /> },
          { path: "submitted-product", element: <SubmittedProduct /> },
          { path: "replied-product", element: <RepliedProduct /> },
          { path: "received-product", element: <ReceivedProduct /> },
          {
            path: "add-new-audit",
            element: <LazyRoute component={AddAuditProduct} />,
          },
          {
            path: "view-audit",
            element: <LazyRoute component={ViewAuditDetails} />,
          },
          {
            path: "",
            element: <Navigate to="initiated-product" />,
            index: true,
          },
        ],
      },
      { path: "/login", element: <AuthRedirect /> },
      { path: "*", element: <NotFound /> },
      { path: "/unauthorized", element: <AccessDenied /> },
    ]);
    return routes;
}

export default AppRouter