// src/store.js
import { configureStore } from '@reduxjs/toolkit';
import layoutReducer from '../shared/redux/slices/layouts/layoutSlice';
import productReducer from '../domains/audit/redux/slices/productSlice';
import {departmentApi} from '../domains/core/redux/api/departmentApi';
import {typeSubTypeApi} from '../domains/core/redux/api/auditTypeSubTypeApi';
import authReducer from '../domains/authentication/redux/slices/AuthSlice';
import userReducer from '../domains/user/redux/slices/userSlice'

// const isDevelopment = import.meta.env.VITE_APP_MODE === 'development';
const isDevelopment = true;
const store = configureStore({
    reducer: {
        Layout: layoutReducer,
        Product: productReducer,
        [departmentApi.reducerPath]: departmentApi.reducer,
        [typeSubTypeApi.reducerPath]: typeSubTypeApi.reducer,
        Authentication: authReducer,
        User: userReducer
    },
    middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware().concat(departmentApi.middleware, typeSubTypeApi.middleware),
    devTools: isDevelopment,
});

export default store;
