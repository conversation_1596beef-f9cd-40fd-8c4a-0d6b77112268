import React, { useState } from 'react';
import HierarchyDataExample from './HierarchyDataExample';

/**
 * Example component demonstrating how to use the HierarchyDataExample component
 */
const HierarchyDataUsage = () => {
  const [hierarchyId, setHierarchyId] = useState('');
  const [showData, setShowData] = useState(false);

  const handleSubmit = (e) => {
    e.preventDefault();
    setShowData(true);
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Hierarchy Data Viewer</h1>
      
      <div className="bg-white p-6 rounded-lg shadow mb-6">
        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label htmlFor="hierarchyId" className="block text-sm font-medium text-gray-700 mb-1">
              Hierarchy ID
            </label>
            <input
              type="text"
              id="hierarchyId"
              value={hierarchyId}
              onChange={(e) => setHierarchyId(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter hierarchy ID"
              required
            />
          </div>
          
          <button
            type="submit"
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Fetch Hierarchy Data
          </button>
        </form>
      </div>
      
      {showData && hierarchyId && (
        <HierarchyDataExample hierarchyId={hierarchyId} />
      )}
    </div>
  );
};

export default HierarchyDataUsage;
