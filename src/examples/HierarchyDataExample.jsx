import React, { useState, useEffect } from 'react';
import hierarchyService from '../domains/smartadmin/services/hierarchyService';

/**
 * Example component demonstrating how to fetch hierarchy data by ID and type
 */
const HierarchyDataExample = ({ hierarchyId }) => {
  const [hierarchyData, setHierarchyData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      if (!hierarchyId) return;
      
      setLoading(true);
      setError(null);
      
      try {
        // Call the fetchHierarchyData function with hierarchyId and type 'N'
        const response = await hierarchyService.fetchHierarchyData(hierarchyId, 'N');
        
        setHierarchyData(response);
      } catch (err) {
        console.error('Error fetching hierarchy data:', err);
        setError('Failed to fetch hierarchy data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [hierarchyId]);

  if (loading) {
    return (
      <div className="p-4 bg-white rounded shadow">
        <div className="flex justify-center">
          <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
        <p className="text-center mt-2 text-gray-600">Loading hierarchy data...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded">
        <p className="text-red-700">{error}</p>
        <button 
          className="mt-2 px-4 py-2 bg-red-100 text-red-700 rounded hover:bg-red-200"
          onClick={() => hierarchyService.fetchHierarchyData(hierarchyId, 'N')}
        >
          Try Again
        </button>
      </div>
    );
  }

  if (!hierarchyData) {
    return (
      <div className="p-4 bg-gray-50 border border-gray-200 rounded">
        <p className="text-gray-600">No hierarchy data available.</p>
      </div>
    );
  }

  return (
    <div className="p-4 bg-white rounded shadow">
      <h2 className="text-xl font-semibold mb-4">Hierarchy Data</h2>
      
      <div className="space-y-4">
        {/* Display hierarchy data properties */}
        <div className="grid grid-cols-2 gap-2">
          <div className="font-medium">ID:</div>
          <div>{hierarchyData.hierarchyId || hierarchyData.id || 'N/A'}</div>
          
          <div className="font-medium">Name:</div>
          <div>{hierarchyData.hierarchyName || hierarchyData.name || 'N/A'}</div>
          
          <div className="font-medium">Code:</div>
          <div>{hierarchyData.hierarchyCode || hierarchyData.code || 'N/A'}</div>
          
          <div className="font-medium">Type:</div>
          <div>{hierarchyData.type || 'N'}</div>
          
          <div className="font-medium">Category:</div>
          <div>{hierarchyData.category || 'N/A'}</div>
          
          <div className="font-medium">Active Status:</div>
          <div>{hierarchyData.activeStatus || 'N/A'}</div>
        </div>
        
        {/* Display additional data if available */}
        {hierarchyData.children && hierarchyData.children.length > 0 && (
          <div className="mt-4">
            <h3 className="text-lg font-medium mb-2">Children</h3>
            <ul className="list-disc pl-5">
              {hierarchyData.children.map((child) => (
                <li key={child.id || child.hierarchyId}>
                  {child.name || child.hierarchyName} ({child.code || child.hierarchyCode})
                </li>
              ))}
            </ul>
          </div>
        )}
        
        {/* Display assigned user if available */}
        {hierarchyData.assignedUser && (
          <div className="mt-4 p-3 bg-blue-50 rounded">
            <h3 className="text-lg font-medium mb-2">Assigned User</h3>
            <div className="grid grid-cols-2 gap-2">
              <div className="font-medium">Name:</div>
              <div>{hierarchyData.assignedUser.name || 'N/A'}</div>
              
              <div className="font-medium">Email:</div>
              <div>{hierarchyData.assignedUser.email || 'N/A'}</div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default HierarchyDataExample;
