pipeline {
    agent { label 'slave-node-2' }
    environment {
        GIT_COMMIT_ID = '' // Placeholder for the Git commit ID
        GIT_CHANGES = '' // Placeholder for Git changes
        GIT_PROJECT_NAME = '' // To store project name
        GIT_BRANCH_NAME = '' // To store branch name
        DOCKER_REGISTRY = 'ap-mumbai-1.ocir.io'
        DOCKER_CREDENTIALS_ID = 'ocir-credentials'
    }
    stages {
        stage('Set Java Home') {
            steps {
                script {
                    // Use the tool command to set the JDK version
                    def javaHome = tool name: 'java21', type: 'jdk'  // The name 'Java21' should match the JDK in Global Tool Configuration

                    // Set the JAVA_HOME environment variable to point to the specified JDK
                    env.JAVA_HOME = javaHome
                    env.PATH = "${javaHome}/bin:${env.PATH}"
                }
            }
        }

        stage('Git Checkout') {
            steps {
                git branch: 'test-merge', credentialsId: 'Git', url: 'https://git.kran.co.in/project/probityfinancials/cg-ifms/frontend/cg-audit-client.git'
            }
        }

        stage('Docker Login') {
            steps {
                script {
                    // Log in to Docker registry using stored credentials
                    docker.withRegistry("https://${DOCKER_REGISTRY}", "${DOCKER_CREDENTIALS_ID}") {
                        echo "Logged in to Docker registry"
                    }
                }
            }
        }

        stage('Docker Build and Push') {
            steps {
                script {
                    // Build and push the Docker image
                    docker.withRegistry("https://${DOCKER_REGISTRY}", "${DOCKER_CREDENTIALS_ID}") {
                        def customImage = docker.build("ap-mumbai-1.ocir.io/bmpz1mmsklkk/auditclient:latest")
                        customImage.push()
                    }
                }
            }
        }
        stage("Deploy to OKE") {
            steps {
                sh "kubectl apply -f audit-cg-deploy.yml"
                // withKubeConfig([credentialsId: 'OKE_KUBECONFIG']) {
                //    sh "kubectl get nodes"
                    // sh "kubectl set image deployment/auditservice auditservice=ap-mumbai-1.ocir.io/bmpz1mmsklkk/auditservice:latest -n default"
                    // sh "kubectl rollout status deployment/auditservice -n default"
                // }
            }
        }    
    }
}

