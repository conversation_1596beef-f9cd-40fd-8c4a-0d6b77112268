# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Node modules
node_modules/

# Build output
dist/
dist-ssr/

# Vite-specific cache
.vite/

# Local environment settings
*.local
#.env
.env.local
.env.*.local

# Editor directories and files
.vscode/
!.vscode/extensions.json
.idea/
.DS_Store

# Miscellaneous IDE files
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Test coverage
coverage/

# Linting cache
.eslintcache

# Optional npm cache directory
.npm/

# Temporary files and directories
*.tmp
*.temp

# Parcel Cache (if using Parcel with Vite)
.cache/

# macOS
.DS_Store

# Windows
Thumbs.db
ehthumbs.db

# Linux
*.lock
