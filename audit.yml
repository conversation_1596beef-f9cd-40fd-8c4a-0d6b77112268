---
apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: default # Set the namespace to default
  labels:
    name: audit
  name: audit-deployment
spec:
  replicas: 2 # Number of replicas to be created
  selector:
    matchLabels:
      name: audit
  template:
    metadata:
      labels:
        name: audit
    spec:
      containers:
        - name: audit
          image: 200530538269.dkr.ecr.ap-south-1.amazonaws.com/cgifms/audit:latest
          ports:
            - containerPort: 81
      imagePullSecrets:
        - name: regcred
---
apiVersion: v1
kind: Service
metadata:
  namespace: default # Set the namespace to default
  labels:
    name: audit-svc
  name: audit
spec:
  selector:
    name: audit
  ports:
    - protocol: "TCP"
      port: 81 # The port the service is running on
      targetPort: 81 # The port exposed by the container
  type: ClusterIP # Default service type for internal communication
---
